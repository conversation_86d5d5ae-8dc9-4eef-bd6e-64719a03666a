<template>
  <div class="audio-recorder">
    <!-- 标题栏 - 使用系统原生控制按钮 -->
    <div class="title-bar">
      <h1 class="title">录音器</h1>
      <div>
        <button class="minimize-btn" @click="minimizeToDesktop" title="最小化到桌面组件">
          <svg viewBox="0 0 24 24" class="icon">
            <path d="M19 13H5v-2h14v2z" />
          </svg>
        </button>
        <!-- 关闭按钮 -->
        <button class="close-btn" @click="closeWindow" title="关闭">
          <svg viewBox="0 0 24 24" class="icon">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
    </div>
    <!-- 录音状态指示器 -->
    <div class="recorder-container">
      <!-- 波形动画背景 -->
      <div class="wave-background" :class="{ active: isRecording }">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>

      <!-- 主录音按钮 -->
      <button
        class="record-button"
        :class="{
          recording: isRecording,
          disabled: isLoading,
        }"
        @click="toggleRecording"
        :disabled="isLoading"
      >
        <!-- 录音图标 -->
        <div class="record-icon" v-if="!isRecording">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"
            />
            <path
              d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"
            />
          </svg>
        </div>

        <!-- 停止图标 -->
        <div class="stop-icon" v-else>
          <svg viewBox="0 0 24 24" fill="currentColor">
            <rect x="6" y="6" width="12" height="12" rx="2" />
          </svg>
        </div>

        <!-- 脉冲动画 -->
        <div class="pulse" v-if="isRecording"></div>
      </button>

      <!-- 录音时长显示 -->
      <div class="duration" v-if="isRecording">
        {{ formatDuration(recordingDuration) }}
      </div>
    </div>

    <!-- 状态文本 -->
    <div class="status-text">
      <span v-if="!isRecording && !isLoading">点击开始录音</span>
      <span v-else-if="isLoading">正在初始化...</span>
      <span v-else-if="isRecording">录音中... 点击停止</span>
    </div>

    <!-- 错误提示 -->
    <div class="error-message" v-if="errorMessage">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { RecordingState } from '@/constants/app.constants'

// 定义组件事件
const emit = defineEmits<{
  recordingComplete: [blob: Blob, duration: number]
  recordingStart: []
  recordingError: [error: string]
}>()

// 响应式数据
const errorMessage = ref<string>('')
const recordingDuration = ref<number>(0)
const durationTimer = ref<number | null>(null)

// 初始化录音器
const { audioState, recOpen, recStop } = useRecorder()

// 计算属性
const isRecording = computed(() => audioState.value === RecordingState.RECORDING)
const isLoading = computed(() => audioState.value === RecordingState.ERROR)

/**
 * 格式化录音时长显示
 * @param seconds 秒数
 * @returns 格式化的时间字符串 (mm:ss)
 */
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * 开始录音时长计时
 */
const startDurationTimer = (): void => {
  recordingDuration.value = 0
  durationTimer.value = window.setInterval(() => {
    recordingDuration.value++
  }, 1000)
}

/**
 * 停止录音时长计时
 */
const stopDurationTimer = (): void => {
  if (durationTimer.value) {
    clearInterval(durationTimer.value)
    durationTimer.value = null
  }
}

/**
 * 开始录音
 */
const startRecording = async (): Promise<void> => {
  try {
    errorMessage.value = ''
    await recOpen()
    startDurationTimer()
    emit('recordingStart')
    console.log('录音开始')
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '录音启动失败'
    errorMessage.value = errorMsg
    emit('recordingError', errorMsg)
    console.error('录音启动失败:', error)
  }
}

/**
 * 停止录音
 */
const stopRecording = (): void => {
  stopDurationTimer()
  recStop((blob: Blob, duration: number) => {
    emit('recordingComplete', blob, duration)
    console.log('录音完成，时长:', duration, '秒')
    console.log('录音文件大小:', blob.size, 'bytes')
  })
}

/**
 * 切换录音状态
 */
const toggleRecording = (): void => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopDurationTimer()
})
</script>

<style scoped>
.audio-recorder {
  display: flex;
  flex-direction: column;
  width: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-height: 250px;
  position: relative;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  margin-bottom: 20px;
  background: rgba(0, 0, 0, 0.1);
  cursor: move;
  -webkit-app-region: drag;
  & > div {
    display: flex;
    align-items: center;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #fff;
  }

  .minimize-btn,
  .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    -webkit-app-region: no-drag;
  }

  .close-btn {
    margin-left: 10px;
  }
  .minimize-btn:hover,
  .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .minimize-btn .icon,
  .close-btn .icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

.recorder-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  width: 200px;
  height: 200px;
}

/* 波形动画背景 */
.wave-background {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.wave-background.active .wave {
  animation: wave-pulse 2s infinite;
}

.wave-1 {
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.wave-2 {
  width: 120px;
  height: 120px;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 160px;
  height: 160px;
  animation-delay: 1s;
}

@keyframes wave-pulse {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 主录音按钮 */
.record-button {
  position: relative;
  width: 80px;
  height: 80px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(145deg, #ff6b6b, #ee5a52);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(238, 90, 82, 0.4);
  z-index: 10;
}

.record-button:hover:not(.disabled) {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(238, 90, 82, 0.6);
}

.record-button.recording {
  background: linear-gradient(145deg, #ff4757, #ff3742);
  animation: recording-pulse 1.5s infinite;
}

.record-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes recording-pulse {
  0%,
  100% {
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 71, 87, 0.8);
  }
}

/* 图标样式 */
.record-icon svg,
.stop-icon svg {
  width: 32px;
  height: 32px;
}

/* 脉冲动画 */
.pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(255, 71, 87, 0.6);
  border-radius: 50%;
  animation: pulse-animation 1.5s infinite;
}

@keyframes pulse-animation {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 时长显示 */
.duration {
  margin-top: 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
}

/* 状态文本 */
.status-text {
  color: white;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 错误信息 */
.error-message {
  color: #ffeb3b;
  background: rgba(255, 235, 59, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
  border: 1px solid rgba(255, 235, 59, 0.3);
}
</style>
