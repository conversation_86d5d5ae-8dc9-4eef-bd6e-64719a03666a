{"version": 3, "sources": ["../../recorder-core/src/engine/wav.js"], "sourcesContent": ["/*\nwav编码器+编码引擎\nhttps://github.com/xiangyuecn/Recorder\n\n当然最佳推荐使用mp3、wav格式，代码也是优先照顾这两种格式\n浏览器支持情况\nhttps://developer.mozilla.org/en-US/docs/Web/HTML/Supported_media_formats\n\n编码原理：给pcm数据加上一个44字节的wav头即成wav文件；pcm数据就是Recorder中的buffers原始数据（重新采样），16位时为LE小端模式（Little Endian），实质上是未经过任何编码处理\n\n注意：其他wav编码器可能不是44字节的头，要从任意wav文件中提取pcm数据，请参考：assets/runtime-codes/fragment.decode.wav.js\n*/\n(function(factory){\n\tvar browser=typeof window==\"object\" && !!window.document;\n\tvar win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面\n\tvar rec=win.Recorder,ni=rec.i18n;\n\tfactory(rec,ni,ni.$T,browser);\n}(function(Recorder,i18n,$T,isBrowser){\n\"use strict\";\n\nRecorder.prototype.enc_wav={\n\tstable:true,fast:true\n\t,getTestMsg:function(){\n\t\treturn $T(\"gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）\");\n\t}\n};\n\nvar NormalizeSet=function(set){\n\tvar bS=set.bitRate,b=bS==8?8:16;\n\tif(bS!=b) Recorder.CLog($T(\"wyw9::WAV Info: 不支持{1}位，已更新成{2}位\",0,bS,b),3);\n\tset.bitRate=b;\n};\n\nRecorder.prototype.wav=function(res,True,False){\n\tvar This=this,set=This.set;\n\t\n\tNormalizeSet(set);\n\tvar size=res.length,sampleRate=set.sampleRate,bitRate=set.bitRate;\n\tvar dataLength=size*(bitRate/8);\n\t\n\t//生成wav头\n\tvar header=Recorder.wav_header(1,1,sampleRate,bitRate,dataLength);\n\tvar offset=header.length;\n\tvar bytes=new Uint8Array(offset+dataLength);\n\tbytes.set(header);\n\t\n\t// 写入采样数据\n\tif(bitRate==8) {\n\t\tfor(var i=0;i<size;i++) {\n\t\t\t//16转8据说是雷霄骅的 https://blog.csdn.net/sevennight1989/article/details/85376149 细节比blqw的按比例的算法清晰点\n\t\t\tvar val=(res[i]>>8)+128;\n\t\t\tbytes[offset++]=val;\n\t\t};\n\t}else{\n\t\tbytes=new Int16Array(bytes.buffer);//长度一定是偶数\n\t\tbytes.set(res,offset/2);\n\t};\n\t\n\tTrue(bytes.buffer,\"audio/wav\");\n};\n\n/**\n根据参数生成wav文件头，返回Uint8Array（format=1时固定返回44字节，其他返回46字节）\nformat: 1 (raw pcm) 2 (ADPCM) 3 (IEEE Float) 6 (g711a) 7 (g711u)\nnumCh: 声道数\ndataLength: wav中的音频数据二进制长度\n**/\nRecorder.wav_header=function(format,numCh,sampleRate,bitRate,dataLength){\n\t//文件头 http://soundfile.sapp.org/doc/WaveFormat/ https://www.jianshu.com/p/63d7aa88582b https://github.com/mattdiamond/Recorderjs https://www.cnblogs.com/blqw/p/3782420.html https://www.cnblogs.com/xiaoqi/p/6993912.html\n\tvar extSize=format==1?0:2;\n\tvar buffer=new ArrayBuffer(44+extSize);\n\tvar data=new DataView(buffer);\n\t\n\tvar offset=0;\n\tvar writeString=function(str){\n\t\tfor (var i=0;i<str.length;i++,offset++) {\n\t\t\tdata.setUint8(offset,str.charCodeAt(i));\n\t\t};\n\t};\n\tvar write16=function(v){\n\t\tdata.setUint16(offset,v,true);\n\t\toffset+=2;\n\t};\n\tvar write32=function(v){\n\t\tdata.setUint32(offset,v,true);\n\t\toffset+=4;\n\t};\n\t\n\t/* RIFF identifier */\n\twriteString('RIFF');\n\t/* RIFF chunk length */\n\twrite32(36+extSize+dataLength);\n\t/* RIFF type */\n\twriteString('WAVE');\n\t/* format chunk identifier */\n\twriteString('fmt ');\n\t/* format chunk length */\n\twrite32(16+extSize);\n\t/* audio format */\n\twrite16(format);\n\t/* channel count */\n\twrite16(numCh);\n\t/* sample rate */\n\twrite32(sampleRate);\n\t/* byte rate (sample rate * block align) */\n\twrite32(sampleRate*(numCh*bitRate/8));// *1 声道\n\t/* block align (channel count * bytes per sample) */\n\twrite16(numCh*bitRate/8);// *1 声道\n\t/* bits per sample */\n\twrite16(bitRate);\n\tif(format!=1){// ExtraParamSize 0\n\t\twrite16(0);\n\t}\n\t/* data chunk identifier */\n\twriteString('data');\n\t/* data chunk length */\n\twrite32(dataLength);\n\t\n\treturn new Uint8Array(buffer);\n};\n\n}));"], "mappings": ";CAYC,SAAS,SAAQ;AACjB,MAAI,UAAQ,OAAO,UAAQ,YAAY,CAAC,CAAC,OAAO;AAChD,MAAI,MAAI,UAAQ,SAAO;AACvB,MAAI,MAAI,IAAI,UAAS,KAAG,IAAI;AAC5B,UAAQ,KAAI,IAAG,GAAG,IAAG,OAAO;AAC7B,GAAE,SAAS,UAAS,MAAK,IAAG,WAAU;AACtC;AAEA,WAAS,UAAU,UAAQ;AAAA,IAC1B,QAAO;AAAA,IAAK,MAAK;AAAA,IAChB,YAAW,WAAU;AACrB,aAAO,GAAG,gHAAgH;AAAA,IAC3H;AAAA,EACD;AAEA,MAAI,eAAa,SAAS,KAAI;AAC7B,QAAI,KAAG,IAAI,SAAQ,IAAE,MAAI,IAAE,IAAE;AAC7B,QAAG,MAAI,EAAG,UAAS,KAAK,GAAG,oCAAmC,GAAE,IAAG,CAAC,GAAE,CAAC;AACvE,QAAI,UAAQ;AAAA,EACb;AAEA,WAAS,UAAU,MAAI,SAAS,KAAI,MAAK,OAAM;AAC9C,QAAI,OAAK,MAAK,MAAI,KAAK;AAEvB,iBAAa,GAAG;AAChB,QAAI,OAAK,IAAI,QAAO,aAAW,IAAI,YAAW,UAAQ,IAAI;AAC1D,QAAI,aAAW,QAAM,UAAQ;AAG7B,QAAI,SAAO,SAAS,WAAW,GAAE,GAAE,YAAW,SAAQ,UAAU;AAChE,QAAI,SAAO,OAAO;AAClB,QAAI,QAAM,IAAI,WAAW,SAAO,UAAU;AAC1C,UAAM,IAAI,MAAM;AAGhB,QAAG,WAAS,GAAG;AACd,eAAQ,IAAE,GAAE,IAAE,MAAK,KAAK;AAEvB,YAAI,OAAK,IAAI,CAAC,KAAG,KAAG;AACpB,cAAM,QAAQ,IAAE;AAAA,MACjB;AAAC;AAAA,IACF,OAAK;AACJ,cAAM,IAAI,WAAW,MAAM,MAAM;AACjC,YAAM,IAAI,KAAI,SAAO,CAAC;AAAA,IACvB;AAAC;AAED,SAAK,MAAM,QAAO,WAAW;AAAA,EAC9B;AAQA,WAAS,aAAW,SAAS,QAAO,OAAM,YAAW,SAAQ,YAAW;AAEvE,QAAI,UAAQ,UAAQ,IAAE,IAAE;AACxB,QAAI,SAAO,IAAI,YAAY,KAAG,OAAO;AACrC,QAAI,OAAK,IAAI,SAAS,MAAM;AAE5B,QAAI,SAAO;AACX,QAAI,cAAY,SAAS,KAAI;AAC5B,eAAS,IAAE,GAAE,IAAE,IAAI,QAAO,KAAI,UAAU;AACvC,aAAK,SAAS,QAAO,IAAI,WAAW,CAAC,CAAC;AAAA,MACvC;AAAC;AAAA,IACF;AACA,QAAI,UAAQ,SAAS,GAAE;AACtB,WAAK,UAAU,QAAO,GAAE,IAAI;AAC5B,gBAAQ;AAAA,IACT;AACA,QAAI,UAAQ,SAAS,GAAE;AACtB,WAAK,UAAU,QAAO,GAAE,IAAI;AAC5B,gBAAQ;AAAA,IACT;AAGA,gBAAY,MAAM;AAElB,YAAQ,KAAG,UAAQ,UAAU;AAE7B,gBAAY,MAAM;AAElB,gBAAY,MAAM;AAElB,YAAQ,KAAG,OAAO;AAElB,YAAQ,MAAM;AAEd,YAAQ,KAAK;AAEb,YAAQ,UAAU;AAElB,YAAQ,cAAY,QAAM,UAAQ,EAAE;AAEpC,YAAQ,QAAM,UAAQ,CAAC;AAEvB,YAAQ,OAAO;AACf,QAAG,UAAQ,GAAE;AACZ,cAAQ,CAAC;AAAA,IACV;AAEA,gBAAY,MAAM;AAElB,YAAQ,UAAU;AAElB,WAAO,IAAI,WAAW,MAAM;AAAA,EAC7B;AAEA,CAAC;", "names": []}