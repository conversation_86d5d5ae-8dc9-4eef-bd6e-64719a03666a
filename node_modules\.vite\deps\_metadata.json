{"hash": "0c95ba03", "configHash": "68933cc9", "lockfileHash": "474a3178", "browserHash": "9c1ccb8c", "optimized": {"pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "c0ab3fd5", "needsInterop": false}, "recorder-core": {"src": "../../recorder-core/src/recorder-core.js", "file": "recorder-core.js", "fileHash": "a5f1947a", "needsInterop": true}, "recorder-core/src/engine/wav": {"src": "../../recorder-core/src/engine/wav.js", "file": "recorder-core_src_engine_wav.js", "fileHash": "8d0a81ac", "needsInterop": true}, "recorder-core/src/extensions/waveview": {"src": "../../recorder-core/src/extensions/waveview.js", "file": "recorder-core_src_extensions_waveview.js", "fileHash": "7e5cc8cb", "needsInterop": true}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "2b23f138", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "e50b9c03", "needsInterop": false}}, "chunks": {"chunk-I4AV3VOL": {"file": "chunk-I4AV3VOL.js"}, "chunk-BUSYA2B4": {"file": "chunk-BUSYA2B4.js"}}}