{"hash": "a373538b", "configHash": "d34714d6", "lockfileHash": "474a3178", "browserHash": "17ed813b", "optimized": {"pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "1c28e00b", "needsInterop": false}, "recorder-core": {"src": "../../recorder-core/src/recorder-core.js", "file": "recorder-core.js", "fileHash": "77e5f17f", "needsInterop": true}, "recorder-core/src/engine/wav": {"src": "../../recorder-core/src/engine/wav.js", "file": "recorder-core_src_engine_wav.js", "fileHash": "7c7efa45", "needsInterop": true}, "recorder-core/src/extensions/waveview": {"src": "../../recorder-core/src/extensions/waveview.js", "file": "recorder-core_src_extensions_waveview.js", "fileHash": "fd0ed699", "needsInterop": true}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "3fdf64ae", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "74d32e47", "needsInterop": false}}, "chunks": {"chunk-I4AV3VOL": {"file": "chunk-I4AV3VOL.js"}, "chunk-BUSYA2B4": {"file": "chunk-BUSYA2B4.js"}}}