# 录音功能使用说明

## 功能概述

本项目实现了一个美观的录音功能，具有以下特点：

- ✅ 简洁的开始/结束录音操作
- ✅ 录制时的动态视觉效果（波形动画、脉冲效果）
- ✅ 实时录音时长显示
- ✅ 录音状态指示
- ✅ 错误处理和用户反馈
- ✅ 录音完成后返回 Blob 对象

## 核心组件

### 1. AudioRecorder.vue
主要的录音界面组件，包含：
- 圆形录音按钮（红色渐变）
- 录制时的波形动画背景
- 脉冲动画效果
- 录音时长计时器
- 状态文本提示

### 2. useRecorder.ts Hook
录音功能的核心逻辑，提供：
- `recOpen()`: 开始录音
- `recStop(callback)`: 停止录音并返回 Blob
- `audioState`: 录音状态管理
- 音频处理和配置

## 使用方法

### 基本使用

```vue
<template>
  <AudioRecorder 
    @recording-complete="handleRecordingComplete"
    @recording-start="handleRecordingStart"
    @recording-error="handleRecordingError"
  />
</template>

<script setup>
import AudioRecorder from '@/components/AudioRecorder.vue'

const handleRecordingComplete = (blob, duration) => {
  console.log('录音完成:', { blob, duration })
  // 处理录音文件...
}
</script>
```

### 事件说明

- `recording-complete`: 录音完成时触发，返回 `(blob: Blob, duration: number)`
- `recording-start`: 录音开始时触发
- `recording-error`: 录音出错时触发，返回 `(error: string)`

## 录音配置

默认录音配置：
- 格式：WAV
- 采样率：44100 Hz
- 比特率：256 kbps
- 声道：单声道
- 启用回声消除、噪声抑制、自动增益控制

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## 权限要求

录音功能需要用户授权麦克风权限。首次使用时浏览器会弹出权限请求。

## 启动项目

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 项目结构

```
src/
├── components/
│   └── AudioRecorder.vue     # 录音组件
├── hooks/
│   └── useRecorder.ts        # 录音功能 Hook
├── constants/
│   └── app.constants.ts      # 录音状态枚举
└── App.vue                   # 主应用组件
```

## 技术栈

- Vue 3 + TypeScript
- Electron
- recorder-core (录音库)
- CSS3 动画

## 注意事项

1. 录音功能需要在 HTTPS 环境或 localhost 下运行
2. 确保用户已授权麦克风权限
3. 录音文件以 Blob 形式返回，可根据需要进行后续处理（保存、上传等）
4. 组件已包含完整的错误处理和用户反馈机制
