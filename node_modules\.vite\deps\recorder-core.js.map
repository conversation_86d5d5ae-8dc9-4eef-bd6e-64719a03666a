{"version": 3, "sources": ["../../recorder-core/src/recorder-core.js"], "sourcesContent": ["/*\n录音\nhttps://github.com/xiangyuecn/Recorder\n*/\n(function(factory){\n\tvar browser=typeof window==\"object\" && !!window.document;\n\tvar win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面\n\tfactory(win,browser);\n\t//umd returnExports.js\n\tif(typeof(define)=='function' && define.amd){\n\t\tdefine(function(){\n\t\t\treturn win.Recorder;\n\t\t});\n\t};\n\tif(typeof(module)=='object' && module.exports){\n\t\tmodule.exports=win.Recorder;\n\t};\n}(function(Export,isBrowser){\n\"use strict\";\n\nvar NOOP=function(){};\nvar IsNum=function(v){return typeof v==\"number\"};\nvar ToJson=function(v){return JSON.stringify(v)};\n\nvar Recorder=function(set){\n\treturn new initFn(set);\n};\nvar LM=Recorder.LM=\"2025-01-11 09:28\";\nvar GitUrl=\"https://github.com/xiangyuecn/Recorder\";\nvar RecTxt=\"Recorder\";\nvar getUserMediaTxt=\"getUserMedia\";\nvar srcSampleRateTxt=\"srcSampleRate\";\nvar sampleRateTxt=\"sampleRate\";\nvar bitRateTxt=\"bitRate\";\nvar CatchTxt=\"catch\";\n\nvar WRec2=Export[RecTxt];//重复加载js\nif(WRec2&&WRec2.LM==LM){\n\tWRec2.CLog(WRec2.i18n.$T(\"K8zP::重复导入{1}\",0,RecTxt),3);\n\treturn;\n};\n\n\n//是否已经打开了全局的麦克风录音，所有工作都已经准备好了，就等接收音频数据了\nRecorder.IsOpen=function(){\n\tvar stream=Recorder.Stream;\n\tif(stream){\n\t\tvar tracks=Tracks_(stream), track=tracks[0];\n\t\tif(track){\n\t\t\tvar state=track.readyState;\n\t\t\treturn state==\"live\"||state==track.LIVE;\n\t\t};\n\t};\n\treturn false;\n};\n/*H5录音时的AudioContext缓冲大小。会影响H5录音时的onProcess调用速率，相对于AudioContext.sampleRate=48000时，4096接近12帧/s，调节此参数可生成比较流畅的回调动画。\n\t取值256, 512, 1024, 2048, 4096, 8192, or 16384\n\t注意，取值不能过低，2048开始不同浏览器可能回调速率跟不上造成音质问题。\n\t一般无需调整，调整后需要先close掉已打开的录音，再open时才会生效。\n*/\nRecorder.BufferSize=4096;\n//销毁已持有的所有全局资源，当要彻底移除Recorder时需要显式的调用此方法\nRecorder.Destroy=function(){\n\tCLog(RecTxt+\" Destroy\");\n\tDisconnect();//断开可能存在的全局Stream、资源\n\t\n\tfor(var k in DestroyList){\n\t\tDestroyList[k]();\n\t};\n};\nvar DestroyList={};\n//登记一个需要销毁全局资源的处理方法\nRecorder.BindDestroy=function(key,call){\n\tDestroyList[key]=call;\n};\n//判断浏览器是否支持录音，随时可以调用。注意：仅仅是检测浏览器支持情况，不会判断和调起用户授权，不会判断是否支持特定格式录音。\nRecorder.Support=function(){\n\tif(!isBrowser) return false;\n\tvar scope=navigator.mediaDevices||{};\n\tif(!scope[getUserMediaTxt]){\n\t\tscope=navigator;\n\t\tscope[getUserMediaTxt]||(scope[getUserMediaTxt]=scope.webkitGetUserMedia||scope.mozGetUserMedia||scope.msGetUserMedia);\n\t};\n\tif(!scope[getUserMediaTxt]){\n\t\treturn false;\n\t};\n\tRecorder.Scope=scope;\n\t\n\tif(!Recorder.GetContext()){\n\t\treturn false;\n\t};\n\treturn true;\n};\n//获取AudioContext对象，如果浏览器不支持将返回null。tryNew=false时返回全局的Recorder.Ctx，Ctx.state可能是closed，仅限用于解码等操作。tryNew=true时会尝试创建新的ctx（不支持close的老浏览器依旧返回全局的），注意用完必须自己调用CloseNewCtx(ctx)关闭；注意：非用户操作（触摸、点击等）时调用返回的ctx.state可能是suspended状态，需要在用户操作时调用ctx.resume恢复成running状态，参考rec的runningContext配置\nRecorder.GetContext=function(tryNew){\n\tif(!isBrowser) return null;\n\tvar AC=window.AudioContext;\n\tif(!AC){\n\t\tAC=window.webkitAudioContext;\n\t};\n\tif(!AC){\n\t\treturn null;\n\t};\n\t\n\tvar ctx=Recorder.Ctx, isNew=0;\n\tif(!ctx){ //241020版本后不再保留打开状态的ctx，原因是iOS不全部关闭时新的ctx有可能不正常\n\t\t//不能反复构造，低版本number of hardware contexts reached maximum (6)\n\t\tctx=Recorder.Ctx=new AC(); isNew=1;\n\t\tRecorder.NewCtxs=Recorder.NewCtxs||[];\n\t\t\n\t\tRecorder.BindDestroy(\"Ctx\",function(){\n\t\t\tvar ctx=Recorder.Ctx;\n\t\t\tif(ctx&&ctx.close){//能关掉就关掉，关不掉就保留着\n\t\t\t\tCloseCtx(ctx);\n\t\t\t\tRecorder.Ctx=0;\n\t\t\t};\n\t\t\tvar arr=Recorder.NewCtxs; Recorder.NewCtxs=[];\n\t\t\tfor(var i=0;i<arr.length;i++)CloseCtx(arr[i]);\n\t\t});\n\t};\n\tif(tryNew && ctx.close){//没法关闭的不允许再创建\n\t\tif(!isNew){\n\t\t\tif(!ctx._useC) CloseCtx(ctx); //关闭全局的，不再保留打开状态\n\t\t\tctx=new AC(); //如果是上面新建的就用上面的，不然就用全新的\n\t\t};\n\t\tctx._useC=1;\n\t\tRecorder.NewCtxs.push(ctx);\n\t};\n\treturn ctx;\n};\n//关闭新创建的AudioContext（之前老版本如果是全局的不关闭，241020版本后全部关闭）\nRecorder.CloseNewCtx=function(ctx){\n\tif(ctx && ctx.close){\n\t\tCloseCtx(ctx);\n\t\tvar arr=Recorder.NewCtxs||[],L=arr.length;\n\t\tfor(var i=0;i<arr.length;i++){\n\t\t\tif(arr[i]==ctx){ arr.splice(i,1); break; }\n\t\t}\n\t\tCLog($T(\"mSxV::剩{1}个GetContext未close\",0,L+\"-1=\"+arr.length),arr.length?3:0);\n\t}\n};\nvar CloseCtx=function(ctx){\n\tif(ctx && ctx.close && !ctx._isC){\n\t\tctx._isC=1;\n\t\tif(ctx.state!=\"closed\"){\n\t\t\ttry{ ctx.close() }catch(e){ CLog(\"ctx close err\",1,e) }\n\t\t}\n\t}\n};\n//当AudioContext的状态是suspended时，调用resume恢复状态，但如果没有用户操作resume可能没有回调，封装解决此回调问题；check(count)返回true继续尝试resume，返回false终止任务（不回调False）\nvar ResumeCtx=Recorder.ResumeCtx=function(ctx,check,True,False){\n\tvar isEnd=0,isBind=0,isLsSC=0,runC=0,EL=\"EventListener\",Tag=\"ResumeCtx \";\n\tvar end=function(err,ok){\n\t\tif(isBind){ bind() }\n\t\tif(!isEnd){ isEnd=1; //回调结果\n\t\t\terr&&False(err,runC);\n\t\t\tok&&True(runC);\n\t\t}\n\t\tif(ok){ //监听后续状态变化\n\t\t\tif(!ctx._LsSC && ctx[\"add\"+EL]) ctx[\"add\"+EL](\"statechange\",run);\n\t\t\tctx._LsSC=1; isLsSC=1;\n\t\t}\n\t};\n\tvar bind=function(add){\n\t\tif(add && isBind) return; isBind=add?1:0;\n\t\tvar types=[\"focus\",\"mousedown\",\"mouseup\",\"touchstart\",\"touchend\"];\n\t\tfor(var i=0;i<types.length;i++)\n\t\t\twindow[(add?\"add\":\"remove\")+EL](types[i],run,true);\n\t};\n\tvar run=function(){\n\t\tvar sVal=ctx.state,spEnd=CtxSpEnd(sVal);\n\t\tif(!isEnd && !check(spEnd?++runC:runC))return end(); //终止，不回调\n\t\tif(spEnd){\n\t\t\tif(isLsSC)CLog(Tag+\"sc \"+sVal,3);\n\t\t\tbind(1); //绑定用户事件尝试恢复\n\t\t\tctx.resume().then(function(){ //resume回调不可靠\n\t\t\t\tif(isLsSC)CLog(Tag+\"sc \"+ctx.state);\n\t\t\t\tend(0,1);\n\t\t\t})[CatchTxt](function(e){ //出错且无法恢复\n\t\t\t\tCLog(Tag+\"error\",1,e);\n\t\t\t\tif(!CtxSpEnd(ctx.state)){\n\t\t\t\t\tend(e.message||\"error\");\n\t\t\t\t}\n\t\t\t});\n\t\t}else if(sVal==\"closed\"){\n\t\t\tif(isLsSC && !ctx._isC)CLog(Tag+\"sc \"+sVal,1); //无法恢复，打个日志\n\t\t\tend(\"ctx closed\");\n\t\t}else{ end(0,1) }; //running 或老的无state\n\t};\n\trun();\n};\nvar CtxSpEnd=Recorder.CtxSpEnd=function(v){\n\treturn v==\"suspended\"||v==\"interrupted\"; //后面这个仅iOS有\n};\nvar CtxState=function(ctx){\n\tvar v=ctx.state,msg=\"ctx.state=\"+v;\n\tif(CtxSpEnd(v))msg+=$T(\"nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）\");\n\treturn msg;\n};\n\n\n/*是否启用MediaRecorder.WebM.PCM来进行音频采集连接（如果浏览器支持的话），默认启用，禁用或者不支持时将使用AudioWorklet或ScriptProcessor来连接；MediaRecorder采集到的音频数据比其他方式更好，几乎不存在丢帧现象，所以音质明显会好很多，建议保持开启*/\nvar ConnectEnableWebM=\"ConnectEnableWebM\";\nRecorder[ConnectEnableWebM]=true;\n\n/*是否启用AudioWorklet特性来进行音频采集连接（如果浏览器支持的话），默认禁用，禁用或不支持时将使用过时的ScriptProcessor来连接（如果方法还在的话），当前AudioWorklet的实现在移动端没有ScriptProcessor稳健；ConnectEnableWebM如果启用并且有效时，本参数将不起作用*/\nvar ConnectEnableWorklet=\"ConnectEnableWorklet\";\nRecorder[ConnectEnableWorklet]=false;\n\n/*初始化H5音频采集连接。如果自行提供了sourceStream将只进行一次简单的连接处理。如果是普通麦克风录音，此时的Stream是全局的，Safari上断开后就无法再次进行连接使用，表现为静音，因此使用全部使用全局处理避免调用到disconnect；全局处理也有利于屏蔽底层细节，start时无需再调用底层接口，提升兼容、可靠性。*/\nvar Connect=function(streamStore){\n\tvar bufferSize=streamStore.BufferSize||Recorder.BufferSize;\n\t\n\tvar stream=streamStore.Stream;\n\tvar ctx=stream._c, ctxSR=ctx[sampleRateTxt], srChunk={};\n\t\n\t//获取音频流信息\n\tvar tracks=Tracks_(stream),track=tracks[0],trackSet=null,tsMsg=\"\";\n\tif(track && track.getSettings){\n\t\ttrackSet=track.getSettings();\n\t\tvar trackSR=trackSet[sampleRateTxt];\n\t\tif(trackSR && trackSR!=ctxSR){\n\t\t\ttsMsg=$T(\"eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，\",0,trackSR,ctxSR);\n\t\t}\n\t}\n\tstream._ts=trackSet;\n\tCLog(tsMsg+\"Stream TrackSet: \"+ToJson(trackSet), tsMsg?3:0);\n\t\n\tvar mediaConn=function(node){\n\t\tvar media=stream._m=ctx.createMediaStreamSource(stream);\n\t\tvar ctxDest=ctx.destination,cmsdTxt=\"createMediaStreamDestination\";\n\t\tif(ctx[cmsdTxt]){\n\t\t\tctxDest=stream._d=ctx[cmsdTxt]();\n\t\t};\n\t\tmedia.connect(node);\n\t\tnode.connect(ctxDest);\n\t}\n\tvar isWebM,isWorklet,badInt,webMTips=\"\";\n\tvar calls=stream._call;\n\t\n\t//浏览器回传的音频数据处理\n\tvar onReceive=function(float32Arr, arrSR){\n\t\tfor(var k0 in calls){//has item\n\t\t\tif(arrSR!=ctxSR){ //MediaRecorder录制的采样率可能和ctx的采样率不同（16k），转换采样率方便统一处理代码也更简单，但音质不会变好，甚至可能会变差一点\n\t\t\t\tsrChunk.index=0;\n\t\t\t\tsrChunk=Recorder.SampleData([float32Arr],arrSR,ctxSR,srChunk,{_sum:1});\n\t\t\t\tvar pcm=srChunk.data;\n\t\t\t\tvar sum=srChunk._sum;\n\t\t\t}else{ //采样率相同，不需要转换采样率\n\t\t\t\tsrChunk={};\n\t\t\t\tvar size=float32Arr.length;\n\t\t\t\t\n\t\t\t\tvar pcm=new Int16Array(size);\n\t\t\t\tvar sum=0;\n\t\t\t\tfor(var j=0;j<size;j++){//floatTo16BitPCM \n\t\t\t\t\tvar s=Math.max(-1,Math.min(1,float32Arr[j]));\n\t\t\t\t\ts=s<0?s*0x8000:s*0x7FFF;\n\t\t\t\t\tpcm[j]=s;\n\t\t\t\t\tsum+=Math.abs(s);\n\t\t\t\t};\n\t\t\t};\n\t\t\t\n\t\t\tfor(var k in calls){\n\t\t\t\tcalls[k](pcm,sum);\n\t\t\t};\n\t\t\t\n\t\t\treturn;\n\t\t};\n\t};\n\t\n\tvar scriptProcessor=\"ScriptProcessor\";//一堆字符串名字，有利于压缩js\n\tvar audioWorklet=\"audioWorklet\";\n\tvar recAudioWorklet=RecTxt+\" \"+audioWorklet;\n\tvar RecProc=\"RecProc\";\n\tvar MediaRecorderTxt=\"MediaRecorder\";\n\tvar MRWebMPCM=MediaRecorderTxt+\".WebM.PCM\";\n\n\n//===================连接方式三=========================\n\t//古董级别的 ScriptProcessor 处理，目前所有浏览器均兼容，虽然是过时的方法，但更稳健，移动端性能比AudioWorklet强\n\tvar oldFn=ctx.createScriptProcessor||ctx.createJavaScriptNode;\n\tvar oldIsBest=$T(\"ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。\",0,audioWorklet);\n\tvar oldScript=function(){\n\t\tisWorklet=stream.isWorklet=false;\n\t\t_Disconn_n(stream);\n\t\tCLog($T(\"7TU0::Connect采用老的{1}，\",0,scriptProcessor)\n\t\t\t+i18n.get(Recorder[ConnectEnableWorklet]?\n\t\t\t\t$T(\"JwCL::但已设置{1}尝试启用{2}\",2)\n\t\t\t\t:$T(\"VGjB::可设置{1}尝试启用{2}\",2)\n\t\t\t\t,[RecTxt+\".\"+ConnectEnableWorklet+\"=true\",audioWorklet]\n\t\t\t)+webMTips+oldIsBest,3);\n\t\t\n\t\tvar process=stream._p=oldFn.call(ctx,bufferSize,1,1);//单声道，省的数据处理复杂\n\t\tmediaConn(process);\n\t\t\n\t\tprocess.onaudioprocess=function(e){\n\t\t\tvar arr=e.inputBuffer.getChannelData(0);\n\t\t\tonReceive(arr, ctxSR);\n\t\t};\n\t};\n\n\n//===================连接方式二=========================\nvar connWorklet=function(){\n\t//尝试开启AudioWorklet处理\n\tisWebM=stream.isWebM=false;\n\t_Disconn_r(stream);\n\t\n\tisWorklet=stream.isWorklet=!oldFn || Recorder[ConnectEnableWorklet];\n\tvar AwNode=window.AudioWorkletNode;\n\tif(!(isWorklet && ctx[audioWorklet] && AwNode)){\n\t\toldScript();//被禁用 或 不支持，直接使用老的\n\t\treturn;\n\t};\n\tvar clazzUrl=function(){\n\t\tvar xf=function(f){return f.toString().replace(/^function|DEL_/g,\"\").replace(/\\$RA/g,recAudioWorklet)};\n\t\tvar clazz='class '+RecProc+' extends AudioWorkletProcessor{';\n\t\t\tclazz+=\"constructor \"+xf(function(option){\n\t\t\t\tDEL_super(option);\n\t\t\t\tvar This=this,bufferSize=option.processorOptions.bufferSize;\n\t\t\t\tThis.bufferSize=bufferSize;\n\t\t\t\tThis.buffer=new Float32Array(bufferSize*2);//乱给size搞乱缓冲区不管\n\t\t\t\tThis.pos=0;\n\t\t\t\tThis.port.onmessage=function(e){\n\t\t\t\t\tif(e.data.kill){\n\t\t\t\t\t\tThis.kill=true;\n\t\t\t\t\t\t$C.log(\"$RA kill call\");\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t$C.log(\"$RA .ctor call\", option);\n\t\t\t});\n\t\t\t\n\t\t\t//https://developer.mozilla.org/en-US/docs/Web/API/AudioWorkletProcessor/process 每次回调128个采样数据，1秒375次回调，高频导致移动端性能问题，结果就是回调次数缺斤少两，进而导致丢失数据，PC端似乎没有性能问题\n\t\t\tclazz+=\"process \"+xf(function(input,b,c){//需要等到ctx激活后才会有回调\n\t\t\t\tvar This=this,bufferSize=This.bufferSize;\n\t\t\t\tvar buffer=This.buffer,pos=This.pos;\n\t\t\t\tinput=(input[0]||[])[0]||[];\n\t\t\t\tif(input.length){\n\t\t\t\t\tbuffer.set(input,pos);\n\t\t\t\t\tpos+=input.length;\n\t\t\t\t\t\n\t\t\t\t\tvar len=~~(pos/bufferSize)*bufferSize;\n\t\t\t\t\tif(len){\n\t\t\t\t\t\tthis.port.postMessage({ val: buffer.slice(0,len) });\n\t\t\t\t\t\t\n\t\t\t\t\t\tvar more=buffer.subarray(len,pos);\n\t\t\t\t\t\tbuffer=new Float32Array(bufferSize*2);\n\t\t\t\t\t\tbuffer.set(more);\n\t\t\t\t\t\tpos=more.length;\n\t\t\t\t\t\tThis.buffer=buffer;\n\t\t\t\t\t}\n\t\t\t\t\tThis.pos=pos;\n\t\t\t\t}\n\t\t\t\treturn !This.kill;\n\t\t\t});\n\t\tclazz+='}'\n\t\t\t+'try{'\n\t\t\t\t+'registerProcessor(\"'+RecProc+'\", '+RecProc+')'\n\t\t\t+'}catch(e){$C.error(\"'+recAudioWorklet+' Reg Error\",e)}';\n\t\tclazz=clazz.replace(/\\$C\\./g,\"console.\");//一些编译器会文本替换日志函数\n\t\t//URL.createObjectURL 本地有些浏览器会报 Not allowed to load local resource，直接用dataurl\n\t\treturn \"data:text/javascript;base64,\"+btoa(unescape(encodeURIComponent(clazz)));\n\t};\n\t\n\tvar awNext=function(){//可以继续，没有调用断开\n\t\treturn isWorklet && stream._na;\n\t};\n\tvar nodeAlive=stream._na=function(){\n\t\t//start时会调用，只要没有收到数据就断定AudioWorklet有问题，恢复用老的\n\t\tif(badInt!==\"\"){//没有回调过数据\n\t\t\tclearTimeout(badInt);\n\t\t\tbadInt=setTimeout(function(){\n\t\t\t\tbadInt=0;\n\t\t\t\tif(awNext()){\n\t\t\t\t\tCLog($T(\"MxX1::{1}未返回任何音频，恢复使用{2}\",0,audioWorklet,scriptProcessor),3);\n\t\t\t\t\toldFn&&oldScript();//未来没有老的，可能是误判\n\t\t\t\t};\n\t\t\t},500);\n\t\t};\n\t};\n\tvar createNode=function(){\n\t\tif(!awNext())return;\n\t\tvar node=stream._n=new AwNode(ctx, RecProc, {\n\t\t\tprocessorOptions:{bufferSize:bufferSize}\n\t\t});\n\t\tmediaConn(node);\n\t\tnode.port.onmessage=function(e){\n\t\t\tif(badInt){\n\t\t\t\tclearTimeout(badInt);badInt=\"\";\n\t\t\t};\n\t\t\tif(awNext()){\n\t\t\t\tonReceive(e.data.val, ctxSR);\n\t\t\t}else if(!isWorklet){\n\t\t\t\tCLog($T(\"XUap::{1}多余回调\",0,audioWorklet),3);\n\t\t\t};\n\t\t};\n\t\tCLog($T(\"yOta::Connect采用{1}，设置{2}可恢复老式{3}\",0,audioWorklet,RecTxt+\".\"+ConnectEnableWorklet+\"=false\",scriptProcessor)+webMTips+oldIsBest,3);\n\t};\n\t\n\t//如果start时的resume和下面的构造node同时进行，将会导致部分浏览器崩溃 (STATUS_ACCESS_VIOLATION)，源码assets中 ztest_chrome_bug_AudioWorkletNode.html 可测试。所以，将所有代码套到resume里面（不管catch），避免出现这个问题\n\tvar ctxOK=function(){\n\t\tif(!awNext())return;\n\t\tif(ctx[RecProc]){\n\t\t\tcreateNode();\n\t\t\treturn;\n\t\t};\n\t\tvar url=clazzUrl();\n\t\tctx[audioWorklet].addModule(url).then(function(e){\n\t\t\tif(!awNext())return;\n\t\t\tctx[RecProc]=1;\n\t\t\tcreateNode();\n\t\t\tif(badInt){//重新计时\n\t\t\t\tnodeAlive();\n\t\t\t};\n\t\t})[CatchTxt](function(e){ //fix 关键字，保证catch压缩时保持字符串形式\n\t\t\tCLog(audioWorklet+\".addModule Error\",1,e);\n\t\t\tawNext()&&oldScript();\n\t\t});\n\t};\n\tResumeCtx(ctx,function(){ return awNext() } ,ctxOK,ctxOK);\n};\n\n\n//===================连接方式一=========================\nvar connWebM=function(){\n\t//尝试开启MediaRecorder录制webm+pcm处理\n\tvar MR=window[MediaRecorderTxt];\n\tvar onData=\"ondataavailable\";\n\tvar webmType=\"audio/webm; codecs=pcm\";\n\tisWebM=stream.isWebM=Recorder[ConnectEnableWebM];\n\t\n\tvar supportMR=MR && (onData in MR.prototype) && MR.isTypeSupported(webmType);\n\twebMTips=supportMR?\"\":$T(\"VwPd::（此浏览器不支持{1}）\",0,MRWebMPCM);\n\tif(!isWebM || !supportMR){\n\t\tconnWorklet(); //被禁用 或 不支持MediaRecorder 或 不支持webm+pcm\n\t\treturn;\n\t}\n\t\n\tvar mrNext=function(){//可以继续，没有调用断开\n\t\treturn isWebM && stream._ra;\n\t};\n\tstream._ra=function(){\n\t\t//start时会调用，只要没有收到数据就断定MediaRecorder有问题，降级处理\n\t\tif(badInt!==\"\"){//没有回调过数据\n\t\t\tclearTimeout(badInt);\n\t\t\tbadInt=setTimeout(function(){\n\t\t\t\t//badInt=0; 保留给nodeAlive继续判断\n\t\t\t\tif(mrNext()){\n\t\t\t\t\tCLog($T(\"vHnb::{1}未返回任何音频，降级使用{2}\",0,MediaRecorderTxt,audioWorklet),3);\n\t\t\t\t\tconnWorklet();\n\t\t\t\t};\n\t\t\t},500);\n\t\t};\n\t};\n\t\n\tvar mrSet=Object.assign({mimeType:webmType}, Recorder.ConnectWebMOptions);\n\tvar mr=stream._r=new MR(stream, mrSet);\n\tvar webmData=stream._rd={};\n\tmr[onData]=function(e){\n\t\t//提取webm中的pcm数据，提取失败就等着badInt超时降级处理\n\t\tvar reader=new FileReader();\n\t\treader.onloadend=function(){\n\t\t\tif(mrNext()){\n\t\t\t\tvar f32arr=WebM_Extract(new Uint8Array(reader.result),webmData);\n\t\t\t\tif(!f32arr)return;\n\t\t\t\tif(f32arr==-1){//无法提取，立即降级\n\t\t\t\t\tconnWorklet();\n\t\t\t\t\treturn;\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif(badInt){\n\t\t\t\t\tclearTimeout(badInt);badInt=\"\";\n\t\t\t\t};\n\t\t\t\tonReceive(f32arr, webmData.webmSR);\n\t\t\t}else if(!isWebM){\n\t\t\t\tCLog($T(\"O9P7::{1}多余回调\",0,MediaRecorderTxt),3);\n\t\t\t};\n\t\t};\n\t\treader.readAsArrayBuffer(e.data);\n\t};\n\ttry{\n\t\tmr.start(~~(bufferSize/48));//按48k时的回调间隔\n\t\tCLog($T(\"LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}\",0,MRWebMPCM,RecTxt+\".\"+ConnectEnableWebM+\"=false\",audioWorklet,scriptProcessor));\n\t}catch(e){ //存在视频流时可能会抛异常\n\t\tCLog(\"mr start err\",1,e);\n\t\tconnWorklet();\n\t};\n};\n\n\tconnWebM();\n};\nvar ConnAlive=function(stream){\n\tif(stream._na) stream._na(); //检查AudioWorklet连接是否有效，无效就回滚到老的ScriptProcessor\n\tif(stream._ra) stream._ra(); //检查MediaRecorder连接是否有效，无效就降级处理\n};\nvar _Disconn_n=function(stream){\n\tstream._na=null;\n\tif(stream._n){\n\t\tstream._n.port.postMessage({kill:true});\n\t\tstream._n.disconnect();\n\t\tstream._n=null;\n\t};\n};\nvar _Disconn_r=function(stream){\n\tstream._ra=null;\n\tif(stream._r){\n\t\ttry{ stream._r.stop() }catch(e){ CLog(\"mr stop err\",1,e) }\n\t\tstream._r=null;\n\t};\n};\nvar Disconnect=function(streamStore){\n\tstreamStore=streamStore||Recorder;\n\tvar isGlobal=streamStore==Recorder;\n\t\n\tvar stream=streamStore.Stream;\n\tif(stream){\n\t\tif(stream._m){\n\t\t\tstream._m.disconnect();\n\t\t\tstream._m=null;\n\t\t};\n\t\tif(!stream._RC && stream._c){//提供的runningContext不处理\n\t\t\tRecorder.CloseNewCtx(stream._c);\n\t\t};\n\t\tstream._RC=null; stream._c=null;\n\t\tif(stream._d){\n\t\t\tStopS_(stream._d.stream);\n\t\t\tstream._d=null;\n\t\t};\n\t\tif(stream._p){\n\t\t\tstream._p.disconnect();\n\t\t\tstream._p.onaudioprocess=stream._p=null;\n\t\t};\n\t\t_Disconn_n(stream);\n\t\t_Disconn_r(stream);\n\t\t\n\t\tif(isGlobal){//全局的时候，要把流关掉（麦克风），直接提供的流不处理\n\t\t\tStopS_(stream);\n\t\t};\n\t};\n\tstreamStore.Stream=0;\n};\n//关闭一个音频流\nvar StopS_=Recorder.StopS_=function(stream){\n\tvar tracks=Tracks_(stream);\n\tfor(var i=0;i<tracks.length;i++){\n\t\tvar track=tracks[i];\n\t\ttrack.stop&&track.stop();\n\t};\n\tstream.stop&&stream.stop();\n};\n//获取流中的所有轨道\nvar Tracks_=function(stream){\n\tvar arr1=0,arr2=0,arr=[];\n\t//stream.getTracks() 得到的sourceStream还得去排序 不然第一个可能是video\n\tif(stream.getAudioTracks){ arr1=stream.getAudioTracks(); arr2=stream.getVideoTracks(); }\n\tif(!arr1){ arr1=stream.audioTracks; arr2=stream.videoTracks; }\n\tfor(var i=0,L=arr1?arr1.length:0;i<L;i++)arr.push(arr1[i]); //音频放前面，方便取[0]\n\tfor(var i=0,L=arr2?arr2.length:0;i<L;i++)arr.push(arr2[i]);\n\treturn arr;\n};\n\n/*对pcm数据的采样率进行转换\npcmDatas: [[Int16,...]] pcm片段列表，二维数组里面是Int16Array，也可传Float32Array（会转成Int16Array）\npcmSampleRate:48000 pcm数据的采样率\nnewSampleRate:16000 需要转换成的采样率，241020版本后支持转成任意采样率，之前老版本newSampleRate>=pcmSampleRate时不会进行任何处理，小于时会进行重新采样\nprevChunkInfo:{} 可选，上次调用时的返回值，用于连续转换，本次调用将从上次结束位置开始进行处理。或可自行定义一个ChunkInfo从pcmDatas指定的位置开始进行转换\noption:{ 可选，配置项\n\t\tframeSize:123456 帧大小，每帧的PCM Int16的数量，采样率转换后的pcm长度为frameSize的整数倍，用于连续转换。目前仅在mp3格式时才有用，frameSize取值为1152，这样编码出来的mp3时长和pcm的时长完全一致，否则会因为mp3最后一帧录音不够填满时添加填充数据导致mp3的时长变长。\n\t\tframeType:\"\" 帧类型，一般为rec.set.type，提供此参数时无需提供frameSize，会自动使用最佳的值给frameSize赋值，目前仅支持mp3=1152(MPEG1 Layer3的每帧采采样数)，其他类型=1。\n\t\t\t以上两个参数用于连续转换时使用，最多使用一个，不提供时不进行帧的特殊处理，提供时必须同时提供prevChunkInfo才有作用。最后一段数据处理时无需提供帧大小以便输出最后一丁点残留数据。\n\t}\n\n返回ChunkInfo:{\n\t//可定义，从指定位置开始转换到结尾\n\tindex:0 pcmDatas已处理到的索引；比如每次都是单个pcm需要连续处理时，可每次调用前重置成0，pcmDatas仅需传入`[pcm]`固定一个元素\n\toffset:0.0 已处理到的index对应的pcm中的偏移的下一个位置（提升采样率时为结果的pcm）\n\traisePrev:null 提升采样率时的前一个pcm结果采样值\n\t\n\t//可定义，指定的一个滤波配置：默认使用Recorder.IIRFilter低通滤波（可有效抑制混叠产生的杂音，小采样率大于高采样率的75%时不默认滤波），如果提供了配置但fn为null时将不滤波；sr、srn为此滤波函数对应的初始化采样率，当采样率和参数的不一致时将重新设为默认函数\n\tfilter:null||{fn:fn(sample),sr:pcmSampleRate,srn:newSampleRate}\n\t\n\t//仅作为返回值\n\tframeNext:null||[Int16,...] 下一帧的部分数据，frameSize设置了的时候才可能会有\n\tsampleRate:16000 结果的采样率=newSampleRate，老版本<=newSampleRate\n\tdata:[Int16,...] 转换后的PCM结果；如果是连续转换，并且pcmDatas中并没有新数据时，data的长度可能为0\n}\n*/\nRecorder.SampleData=function(pcmDatas,pcmSampleRate,newSampleRate,prevChunkInfo,option){\n\tvar Txt=\"SampleData\";\n\tprevChunkInfo||(prevChunkInfo={});\n\tvar index=prevChunkInfo.index||0;\n\tvar offset=prevChunkInfo.offset||0;\n\tvar raisePrev=prevChunkInfo.raisePrev||0;\n\t\n\tvar filter=prevChunkInfo.filter;\n\tif(filter&&filter.fn&&(filter.sr&&filter.sr!=pcmSampleRate || filter.srn&&filter.srn!=newSampleRate)){\n\t\tfilter=null; CLog($T(\"d48C::{1}的filter采样率变了，重设滤波\",0,Txt),3);\n\t};\n\tif(!filter){ //采样率差距比较大才开启低通滤波\n\t\tif(newSampleRate<=pcmSampleRate){ //降低采样率或不变，最高频率用新采样率频率的3/4\n\t\t\tvar freq=newSampleRate>pcmSampleRate*3/4?0: newSampleRate/2 *3/4;\n\t\t\tfilter={fn:freq?Recorder.IIRFilter(true,pcmSampleRate,freq):0};\n\t\t}else{ //提升采样率，最高频率用原始采样率频率的3/4\n\t\t\tvar freq=pcmSampleRate>newSampleRate*3/4?0: pcmSampleRate/2 *3/4;\n\t\t\tfilter={fn:freq?Recorder.IIRFilter(true,newSampleRate,freq):0};\n\t\t};\n\t};\n\tfilter.sr=pcmSampleRate;\n\tfilter.srn=newSampleRate;\n\tvar filterFn=filter.fn;\n\t\n\tvar frameNext=prevChunkInfo.frameNext||[];\n\toption||(option={});\n\tvar frameSize=option.frameSize||1;\n\tif(option.frameType){\n\t\tframeSize=option.frameType==\"mp3\"?1152:1;\n\t};\n\tvar useSum=option._sum, _sum=0; //内部用的，sum不考虑配置了frame\n\t\n\tvar nLen=pcmDatas.length;\n\tif(index>nLen+1){\n\t\tCLog($T(\"tlbC::{1}似乎传入了未重置chunk {2}\",0,Txt,index+\">\"+nLen),3);\n\t};\n\tvar size=0;\n\tfor(var i=index;i<nLen;i++){\n\t\tsize+=pcmDatas[i].length;\n\t};\n\t\n\t//采样 https://www.cnblogs.com/blqw/p/3782420.html\n\tvar step=pcmSampleRate/newSampleRate;\n\tif(step>1){//新采样低于录音采样，进行抽样\n\t\tsize=Math.max(0,size-Math.floor(offset));\n\t\tsize=Math.floor(size/step);\n\t}else if(step<1){//新采样高于录音采样，插值处理\n\t\tvar raiseStep=1/step;\n\t\tsize=Math.floor(size*raiseStep);\n\t};\n\t\n\tsize+=frameNext.length;\n\tvar res=new Int16Array(size);\n\tvar idx=0;\n\t//添加上一次不够一帧的剩余数据\n\tfor(var i=0;i<frameNext.length;i++){\n\t\tres[idx]=frameNext[i];\n\t\tidx++;\n\t};\n\t//处理数据\n\tfor (;index<nLen;index++) {\n\t\tvar o=pcmDatas[index], isF32=o instanceof Float32Array;\n\t\tvar i=offset,il=o.length;\n\t\tvar F=filterFn&&filterFn.Embed,F1=0,F2=0,Fx=0,Fy=0;//低通滤波后的数据\n\t\t\n\t\tif(step<1){ //提升采样率\n\t\t\tvar idx1=idx+i, prev=raisePrev;\n\t\t\tfor(var i0=0;i0<il;i0++){\n\t\t\t\tvar oVal=o[i0];\n\t\t\t\tif(isF32){//floatTo16BitPCM \n\t\t\t\t\toVal=Math.max(-1,Math.min(1,oVal));\n\t\t\t\t\toVal=oVal<0?oVal*0x8000:oVal*0x7FFF;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar pos=Math.floor(idx1);\n\t\t\t\tidx1+=raiseStep;\n\t\t\t\tvar end=Math.floor(idx1);\n\t\t\t\n\t\t\t\t//简单的从prev平滑填充到cur，有效减少转换引入的杂音\n\t\t\t\tvar n=(oVal-prev)/(end-pos);\n\t\t\t\tfor(var j=1;pos<end;pos++,j++){\n\t\t\t\t\tvar s=Math.floor(prev+(j*n));\n\t\t\t\t\tif(F){//IIRFilter代码内置，比函数调用快4倍\n\t\t\t\t\t\tFx=s;\n\t\t\t\t\t\tFy=F.b0 * Fx + F.b1 * F.x1 + F.b0 * F.x2 - F.a1 * F.y1 - F.a2 * F.y2;\n\t\t\t\t\t\tF.x2 = F.x1; F.x1 = Fx; F.y2 = F.y1; F.y1 = Fy;\n\t\t\t\t\t\ts=Fy;\n\t\t\t\t\t}else{ s=filterFn?filterFn(s):s; }\n\t\t\t\t\t\n\t\t\t\t\tif(s>0x7FFF) s=0x7FFF; else if(s<-0x8000) s=-0x8000; //Int16越界处理\n\t\t\t\t\tif(useSum) _sum+=Math.abs(s);\n\t\t\t\t\tres[pos]=s;\n\t\t\t\t\tidx++;\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tprev=raisePrev=oVal;\n\t\t\t\ti+=raiseStep;//插值\n\t\t\t}\n\t\t\toffset=i%1;\n\t\t\tcontinue;\n\t\t};\n\t\t//降低采样率或不变\n\t\tfor(var i0=0,i2=0;i0<il;i0++,i2++){\n\t\t\tif(i2<il){\n\t\t\t\tvar oVal=o[i2];\n\t\t\t\tif(isF32){//floatTo16BitPCM \n\t\t\t\t\toVal=Math.max(-1,Math.min(1,oVal));\n\t\t\t\t\toVal=oVal<0?oVal*0x8000:oVal*0x7FFF;\n\t\t\t\t}\n\t\t\t\tif(F){//IIRFilter代码内置，比函数调用快4倍\n\t\t\t\t\tFx=oVal;\n\t\t\t\t\tFy=F.b0 * Fx + F.b1 * F.x1 + F.b0 * F.x2 - F.a1 * F.y1 - F.a2 * F.y2;\n\t\t\t\t\tF.x2 = F.x1; F.x1 = Fx; F.y2 = F.y1; F.y1 = Fy;\n\t\t\t\t}else{ Fy=filterFn?filterFn(oVal):oVal; }\n\t\t\t}\n\t\t\tF1=F2; F2=Fy;\n\t\t\tif(i2==0){ i0--; continue; } //首次只计算o[0]\n\t\t\t\n\t\t\t//https://www.cnblogs.com/xiaoqi/p/6993912.html\n\t\t\t//当前点=当前点+到后面一个点之间的增量，音质比直接简单抽样好些\n\t\t\tvar before = Math.floor(i);\n\t\t\tif(i0!=before)continue;\n\t\t\tvar after = Math.ceil(i);\n\t\t\tvar atPoint = i - before;\n\t\t\t\n\t\t\tvar beforeVal=F1;\n\t\t\tvar afterVal=after<il ? F2 : beforeVal; //后个点越界了，忽略不计\n\t\t\tvar val=beforeVal+(afterVal-beforeVal)*atPoint;\n\t\t\t\n\t\t\tif(val>0x7FFF) val=0x7FFF; else if(val<-0x8000) val=-0x8000; //Int16越界处理\n\t\t\tif(useSum) _sum+=Math.abs(val);\n\t\t\tres[idx]=val;\n\t\t\t\n\t\t\tidx++;\n\t\t\ti+=step;//抽样\n\t\t};\n\t\toffset=Math.max(0, i-il); //不太可能出现负数\n\t};\n\tif(step<1 && idx+1==size){ //提升采样率时可能缺1个，直接删除结尾1个\n\t\tsize--; res=new Int16Array(res.buffer.slice(0, size*2));\n\t};\n\tif(idx-1!=size && idx!=size)CLog(Txt+\" idx:\"+idx+\" != size:\"+size,3); //越界1个\n\t\n\t//帧处理\n\tframeNext=null;\n\tvar frameNextSize=size%frameSize;\n\tif(frameNextSize>0){\n\t\tvar u8Pos=(size-frameNextSize)*2;\n\t\tframeNext=new Int16Array(res.buffer.slice(u8Pos));\n\t\tres=new Int16Array(res.buffer.slice(0,u8Pos));\n\t};\n\t\n\tvar obj={\n\t\tindex:index\n\t\t,offset:offset\n\t\t,raisePrev:raisePrev\n\t\t,filter:filter\n\t\t\n\t\t,frameNext:frameNext\n\t\t,sampleRate:newSampleRate\n\t\t,data:res\n\t};\n\tif(useSum) obj._sum=_sum;\n\treturn obj;\n};\n\n/*IIR低通、高通滤波，移植自：https://gitee.com/52jian/digital-audio-filter AudioFilter.java\n\tuseLowPass: true或false，true为低通滤波，false为高通滤波\n\tsampleRate: 待处理pcm的采样率\n\tfreq: 截止频率Hz，最大频率为sampleRate/2，低通时会切掉高于此频率的声音，高通时会切掉低于此频率的声音，注意滤波并非100%的切掉不需要的声音，而是减弱频率对应的声音，离截止频率越远对应声音减弱越厉害，离截止频率越近声音就几乎无衰减\n\t返回的是一个函数，用此函数对pcm的每个采样值按顺序进行处理即可（不同pcm不可共用）；注意此函数返回值可能会越界超过Int16范围，自行限制一下即可：Math.min(Math.max(val,-0x8000),0x7FFF)\n可重新赋值一个函数，来改变Recorder的默认行为，比如SampleData中的低通滤波*/\nRecorder.IIRFilter=function(useLowPass, sampleRate, freq){\n\tvar ov = 2 * Math.PI * freq / sampleRate;\n\tvar sn = Math.sin(ov);\n\tvar cs = Math.cos(ov);\n\tvar alpha = sn / 2;\n\t\n\tvar a0 = 1 + alpha;\n\tvar a1 = (-2 * cs) / a0;\n\tvar a2 = (1 - alpha) / a0;\n\tif(useLowPass){\n\t\tvar b0 = (1 - cs) / 2 / a0;\n\t\tvar b1 = (1 - cs) / a0;\n\t}else{\n\t\tvar b0 = (1 + cs) / 2 / a0;\n\t\tvar b1 = -(1 + cs) / a0;\n\t}\n\t\n\tvar x1=0,x2=0,y=0,y1=0,y2=0;\n\tvar fn=function(x){\n\t\ty = b0 * x + b1 * x1 + b0 * x2 - a1 * y1 - a2 * y2;\n\t\tx2 = x1; x1 = x;\n\t\ty2 = y1; y1 = y;\n\t\treturn y;\n\t};\n\tfn.Embed={x1:0,x2:0,y1:0,y2:0,b0:b0,b1:b1,a1:a1,a2:a2};\n\treturn fn;\n};\n\n\n/*计算音量百分比的一个方法\npcmAbsSum: pcm Int16所有采样的绝对值的和\npcmLength: pcm长度\n返回值：0-100，主要当做百分比用\n注意：这个不是分贝，因此没用volume当做名称*/\nRecorder.PowerLevel=function(pcmAbsSum,pcmLength){\n\t/*计算音量 https://blog.csdn.net/jody1989/article/details/73480259\n\t更高灵敏度算法:\n\t\t限定最大感应值10000\n\t\t\t线性曲线：低音量不友好\n\t\t\t\tpower/10000*100 \n\t\t\t对数曲线：低音量友好，但需限定最低感应值\n\t\t\t\t(1+Math.log10(power/10000))*100\n\t*/\n\tvar power=(pcmAbsSum/pcmLength) || 0;//NaN\n\tvar level;\n\tif(power<1251){//1250的结果10%，更小的音量采用线性取值\n\t\tlevel=Math.round(power/1250*10);\n\t}else{\n\t\tlevel=Math.round(Math.min(100,Math.max(0,(1+Math.log(power/10000)/Math.log(10))*100)));\n\t};\n\treturn level;\n};\n\n/*计算音量，单位dBFS（满刻度相对电平）\nmaxSample: 为16位pcm采样的绝对值中最大的一个（计算峰值音量），或者为pcm中所有采样的绝对值的平局值\n返回值：-100~0 （最大值0dB，最小值-100代替-∞）\n*/\nRecorder.PowerDBFS=function(maxSample){\n\tvar val=Math.max(0.1, maxSample||0),Pref=0x7FFF;\n\tval=Math.min(val,Pref);\n\t//https://www.logiclocmusic.com/can-you-tell-the-decibel/\n\t//https://blog.csdn.net/qq_17256689/article/details/120442510\n\tval=20*Math.log(val/Pref)/Math.log(10);\n\treturn Math.max(-100,Math.round(val));\n};\n\n\n\n\n//带时间的日志输出，可设为一个空函数来屏蔽日志输出\n//CLog(msg,errOrLogMsg, logMsg...) err为数字时代表日志类型1:error 2:log默认 3:warn，否则当做内容输出，第一个参数不能是对象因为要拼接时间，后面可以接无数个输出参数\nRecorder.CLog=function(msg,err){\n\tif(typeof console!=\"object\")return;\n\tvar now=new Date();\n\tvar t=(\"0\"+now.getMinutes()).substr(-2)\n\t\t+\":\"+(\"0\"+now.getSeconds()).substr(-2)\n\t\t+\".\"+(\"00\"+now.getMilliseconds()).substr(-3);\n\tvar recID=this&&this.envIn&&this.envCheck&&this.id;\n\tvar arr=[\"[\"+t+\" \"+RecTxt+(recID?\":\"+recID:\"\")+\"]\"+msg];\n\tvar a=arguments,cwe=Recorder.CLog;\n\tvar i=2,fn=cwe.log||console.log;\n\tif(IsNum(err)){\n\t\tfn=err==1?cwe.error||console.error:err==3?cwe.warn||console.warn:fn;\n\t}else{\n\t\ti=1;\n\t};\n\tfor(;i<a.length;i++){\n\t\tarr.push(a[i]);\n\t};\n\tif(IsLoser){//古董浏览器，仅保证基本的可执行不代码异常\n\t\tfn&&fn(\"[IsLoser]\"+arr[0],arr.length>1?arr:\"\");\n\t}else{\n\t\tfn.apply(console,arr);\n\t};\n};\nvar CLog=function(){ Recorder.CLog.apply(this,arguments); };\nvar IsLoser=true;try{IsLoser=!console.log.apply;}catch(e){};\n\n\n\n\nvar ID=0;\nfunction initFn(set){\n\tvar This=this; This.id=++ID;\n\t\n\t//如果开启了流量统计，这里将发送一个图片请求\n\tTraffic();\n\t\n\t\n\tvar o={\n\t\ttype:\"mp3\" //输出类型：mp3,wav，wav输出文件尺寸超大不推荐使用，但mp3编码支持会导致js文件超大，如果不需支持mp3可以使js文件大幅减小\n\t\t//,bitRate:16 //比特率 wav:16或8位，MP3：8kbps 1k/s，8kbps 2k/s 录音文件很小\n\t\t\n\t\t//,sampleRate:16000 //采样率，wav格式大小=sampleRate*时间；mp3此项对低比特率有影响，高比特率几乎无影响。\n\t\t\t\t\t//wav任意值，mp3取值范围：48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000\n\t\t\t\t\t//采样率参考https://www.cnblogs.com/devin87/p/mp3-recorder.html\n\t\t\n\t\t,onProcess:NOOP //fn(buffers,powerLevel,bufferDuration,bufferSampleRate,newBufferIdx,asyncEnd) buffers=[[Int16,...],...]：缓冲的PCM数据，为从开始录音到现在的所有pcm片段；powerLevel：当前缓冲的音量级别0-100，bufferDuration：已缓冲时长，bufferSampleRate：缓冲使用的采样率（当type支持边录边转码(Worker)时，此采样率和设置的采样率相同，否则不一定相同）；newBufferIdx:本次回调新增的buffer起始索引；asyncEnd:fn() 如果onProcess是异步的(返回值为true时)，处理完成时需要调用此回调，如果不是异步的请忽略此参数，此方法回调时必须是真异步（不能真异步时需用setTimeout包裹）。onProcess返回值：如果返回true代表开启异步模式，在某些大量运算的场合异步是必须的，必须在异步处理完成时调用asyncEnd(不能真异步时需用setTimeout包裹)，在onProcess执行后新增的buffer会全部替换成空数组，因此本回调开头应立即将newBufferIdx到本次回调结尾位置的buffer全部保存到另外一个数组内，处理完成后写回buffers中本次回调的结尾位置。\n\t\t\n\t\t//*******高级设置******\n\t\t//,sourceStream:MediaStream Object\n\t\t\t\t//可选直接提供一个媒体流，从这个流中录制、实时处理音频数据（当前Recorder实例独享此流）；不提供时为普通的麦克风录音，由getUserMedia提供音频流（所有Recorder实例共享同一个流）\n\t\t\t\t//比如：audio、video标签dom节点的captureStream方法（实验特性，不同浏览器支持程度不高）返回的流；WebRTC中的remote流；自己创建的流等\n\t\t\t\t//注意：流内必须至少存在一条音轨(Audio Track)，比如audio标签必须等待到可以开始播放后才会有音轨，否则open会失败\n\t\t\n\t\t//,runningContext:AudioContext\n\t\t\t\t//可选提供一个state为running状态的AudioContext对象(ctx)；默认会在rec.open时自动创建一个新的ctx，无用户操作（触摸、点击等）时调用rec.open的ctx.state可能为suspended，会在rec.start时尝试进行ctx.resume，如果也无用户操作ctx.resume可能不会恢复成running状态（目前仅iOS上有此兼容性问题），导致无法去读取媒体流，这时请提前在用户操作时调用Recorder.GetContext(true)来得到一个running状态AudioContext（用完需调用CloseNewCtx(ctx)关闭）\n\t\t\n\t\t//,audioTrackSet:{ deviceId:\"\",groupId:\"\", autoGainControl:true, echoCancellation:true, noiseSuppression:true }\n\t\t\t\t//普通麦克风录音时getUserMedia方法的audio配置参数，比如指定设备id，回声消除、降噪开关；注意：提供的任何配置值都不一定会生效\n\t\t\t\t//由于麦克风是全局共享的，所以新配置后需要close掉以前的再重新open\n\t\t\t\t//同样可配置videoTrackSet，更多参考: https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints\n\t\t\n\t\t//,disableEnvInFix:false 内部参数，禁用设备卡顿时音频输入丢失补偿功能\n\t\t\n\t\t//,takeoffEncodeChunk:NOOP //fn(chunkBytes) chunkBytes=[Uint8,...]：实时编码环境下接管编码器输出，当编码器实时编码出一块有效的二进制音频数据时实时回调此方法；参数为二进制的Uint8Array，就是编码出来的音频数据片段，所有的chunkBytes拼接在一起即为完整音频。本实现的想法最初由QQ2543775048提出\n\t\t\t\t//当提供此回调方法时，将接管编码器的数据输出，编码器内部将放弃存储生成的音频数据；如果当前编码器或环境不支持实时编码处理，将在open时直接走fail逻辑\n\t\t\t\t//因此提供此回调后调用stop方法将无法获得有效的音频数据，因为编码器内没有音频数据，因此stop时返回的blob将是一个字节长度为0的blob\n\t\t\t\t//大部分录音格式编码器都支持实时编码（边录边转码），比如mp3格式：会实时的将编码出来的mp3片段通过此方法回调，所有的chunkBytes拼接到一起即为完整的mp3，此种拼接的结果比mock方法实时生成的音质更加，因为天然避免了首尾的静默\n\t\t\t\t//不支持实时编码的录音格式不可以提供此回调（wav格式不支持，因为wav文件头中需要提供文件最终长度），提供了将在open时直接走fail逻辑\n\t};\n\t\n\tfor(var k in set){\n\t\to[k]=set[k];\n\t};\n\tThis.set=o;\n\t\n\tvar vB=o[bitRateTxt],vS=o[sampleRateTxt]; //校验配置参数\n\tif(vB&&!IsNum(vB) || vS&&!IsNum(vS)){\n\t\tThis.CLog($T.G(\"IllegalArgs-1\",[$T(\"VtS4::{1}和{2}必须是数值\",0,sampleRateTxt,bitRateTxt)]),1,set);\n\t};\n\to[bitRateTxt]=+vB||16;\n\to[sampleRateTxt]=+vS||16000;\n\t\n\tThis.state=0;//运行状态，0未录音 1录音中 2暂停 3等待ctx激活\n\tThis._S=9;//stop同步锁，stop可以阻止open过程中还未运行的start\n\tThis.Sync={O:9,C:9};//和Recorder.Sync一致，只不过这个是非全局的，仅用来简化代码逻辑，无实际作用\n};\n//同步锁，控制对Stream的竞争；用于close时中断异步的open；一个对象open如果变化了都要阻止close，Stream的控制权交个新的对象\nRecorder.Sync={/*open*/O:9,/*close*/C:9};\n\nRecorder.prototype=initFn.prototype={\n\tCLog:CLog\n\t\n\t//流相关的数据存储在哪个对象里面；如果提供了sourceStream，数据直接存储在当前对象中，否则存储在全局\n\t,_streamStore:function(){\n\t\tif(this.set.sourceStream){\n\t\t\treturn this;\n\t\t}else{\n\t\t\treturn Recorder;\n\t\t}\n\t}\n\t//当前实例用到的Stream，可能是全局的，也可能是独享的\n\t,_streamGet:function(){\n\t\treturn this._streamStore().Stream;\n\t}\n\t//当前实例用到的AudioContext，可能是全局的，也可能是独享的\n\t,_streamCtx:function(){\n\t\tvar m=this._streamGet();\n\t\treturn m&&m._c;\n\t}\n\t\n\t//打开录音资源True(),False(msg,isUserNotAllow)，需要调用close。注意：此方法是异步的；一般使用时打开，用完立即关闭；可重复调用，可用来测试是否能录音；open和start至少有一个应当在用户操作（触摸、点击等）下进行调用，原因参考runningContext配置\n\t,open:function(True,False){\n\t\tvar This=this,set=This.set,streamStore=This._streamStore(),newCtx=0;\n\t\tTrue=True||NOOP;\n\t\tvar failCall=function(errMsg,isUserNotAllow){\n\t\t\tisUserNotAllow=!!isUserNotAllow;\n\t\t\tThis.CLog($T(\"5tWi::录音open失败：\")+errMsg+\",isUserNotAllow:\"+isUserNotAllow,1);\n\t\t\tif(newCtx)Recorder.CloseNewCtx(newCtx);\n\t\t\tFalse&&False(errMsg,isUserNotAllow);\n\t\t};\n\t\t\n\t\tThis._streamTag=getUserMediaTxt;\n\t\tvar ok=function(){\n\t\t\tThis.CLog(\"open ok, id:\"+This.id+\" stream:\"+This._streamTag);\n\t\t\tTrue();\n\t\t\t\n\t\t\tThis._SO=0;//解除stop对open中的start调用的阻止\n\t\t};\n\t\t\n\t\t\n\t\t//同步锁\n\t\tvar Lock=streamStore.Sync;\n\t\tvar lockOpen=++Lock.O,lockClose=Lock.C;\n\t\tThis._O=This._O_=lockOpen;//记住当前的open，如果变化了要阻止close，这里假定了新对象已取代当前对象并且不再使用\n\t\tThis._SO=This._S;//记住open过程中的stop，中途任何stop调用后都不能继续open中的start\n\t\tvar lockFail=function(){\n\t\t\t//允许多次open，但不允许任何一次close，或者自身已经调用了关闭\n\t\t\tif(lockClose!=Lock.C || !This._O){\n\t\t\t\tvar err=$T(\"dFm8::open被取消\");\n\t\t\t\tif(lockOpen==Lock.O){\n\t\t\t\t\t//无新的open，已经调用了close进行取消，此处应让上次的close明确生效\n\t\t\t\t\tThis.close();\n\t\t\t\t}else{\n\t\t\t\t\terr=$T(\"VtJO::open被中断\");\n\t\t\t\t};\n\t\t\t\tfailCall(err);\n\t\t\t\treturn true;\n\t\t\t};\n\t\t};\n\t\t\n\t\t//环境配置检查\n\t\tif(!isBrowser){\n\t\t\tfailCall($T.G(\"NonBrowser-1\",[\"open\"])+$T(\"EMJq::，可尝试使用RecordApp解决方案\")+\"(\"+GitUrl+\"/tree/master/app-support-sample)\");\n\t\t\treturn;\n\t\t};\n\t\tvar checkMsg=This.envCheck({envName:\"H5\",canProcess:true});\n\t\tif(checkMsg){\n\t\t\tfailCall($T(\"A5bm::不能录音：\")+checkMsg);\n\t\t\treturn;\n\t\t};\n\t\t\n\t\t//尽量先创建好ctx，不然异步下创建可能不是running状态\n\t\tvar ctx;\n\t\tvar getCtx=function(){\n\t\t\tctx=set.runningContext;\n\t\t\tif(!ctx)ctx=newCtx=Recorder.GetContext(true); //2023-06 尽量创建新的ctx，免得Safari再次连接无回调\n\t\t};\n\t\t\n\t\t//***********已直接提供了音频流************\n\t\tif(set.sourceStream){\n\t\t\tThis._streamTag=\"set.sourceStream\";\n\t\t\tif(!Recorder.GetContext()){\n\t\t\t\tfailCall($T(\"1iU7::不支持此浏览器从流中获取录音\"));\n\t\t\t\treturn;\n\t\t\t};\n\t\t\tgetCtx();\n\t\t\t\n\t\t\tDisconnect(streamStore);//可能已open过，直接先尝试断开\n\t\t\tvar stream=This.Stream=set.sourceStream;\n\t\t\tstream._c=ctx;\n\t\t\tstream._RC=set.runningContext;\n\t\t\tstream._call={};\n\t\t\t\n\t\t\ttry{\n\t\t\t\tConnect(streamStore);\n\t\t\t}catch(e){\n\t\t\t\tDisconnect(streamStore);\n\t\t\t\tfailCall($T(\"BTW2::从流中打开录音失败：\")+e.message);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tok();\n\t\t\treturn;\n\t\t};\n\t\t\n\t\t\n\t\t//***********打开麦克风得到全局的音频流************\n\t\tvar codeFail=function(code,msg){\n\t\t\ttry{//跨域的优先检测一下\n\t\t\t\twindow.top.a;\n\t\t\t}catch(e){\n\t\t\t\tfailCall($T(\"Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})\",0,'allow=\"camera;microphone\"'));\n\t\t\t\treturn;\n\t\t\t};\n\t\t\t\n\t\t\tif(codeErr1(1,code)){\n\t\t\t\tif(/Found/i.test(code)){//可能是非安全环境导致的没有设备\n\t\t\t\t\tfailCall(msg+$T(\"jBa9::，无可用麦克风\"));\n\t\t\t\t}else{\n\t\t\t\t\tfailCall(msg);\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t\tvar codeErr1=function(call,code){ //排除几个明确原因的错误\n\t\t\tif(/Permission|Allow/i.test(code)){\n\t\t\t\tif(call) failCall($T(\"gyO5::用户拒绝了录音权限\"),true);\n\t\t\t}else if(window.isSecureContext===false){\n\t\t\t\tif(call) failCall($T(\"oWNo::浏览器禁止不安全页面录音，可开启https解决\"));\n\t\t\t}else{\n\t\t\t\treturn 1;\n\t\t\t};\n\t\t};\n\t\t\n\t\t\n\t\t//如果已打开并且有效就不要再打开了\n\t\tif(Recorder.IsOpen()){\n\t\t\tok();\n\t\t\treturn;\n\t\t};\n\t\tif(!Recorder.Support()){\n\t\t\tcodeFail(\"\",$T(\"COxc::此浏览器不支持录音\"));\n\t\t\treturn;\n\t\t};\n\t\tgetCtx();\n\t\t\n\t\t//请求权限，如果从未授权，一般浏览器会弹出权限请求弹框\n\t\tvar f1=function(stream){ //请求成功回调\n\t\t\t//https://github.com/xiangyuecn/Recorder/issues/14 获取到的track.readyState!=\"live\"，刚刚回调时可能是正常的，但过一下可能就被关掉了，原因不明。延迟一下保证真异步。对正常浏览器不影响\n\t\t\tsetTimeout(function(){\n\t\t\t\tstream._call={};\n\t\t\t\tvar oldStream=Recorder.Stream;\n\t\t\t\tif(oldStream){\n\t\t\t\t\tDisconnect(); //直接断开已存在的，旧的Connect未完成会自动终止\n\t\t\t\t\tstream._call=oldStream._call;\n\t\t\t\t};\n\t\t\t\tRecorder.Stream=stream;\n\t\t\t\tstream._c=ctx;\n\t\t\t\tstream._RC=set.runningContext;\n\t\t\t\tif(lockFail())return;\n\t\t\t\t\n\t\t\t\tif(Recorder.IsOpen()){\n\t\t\t\t\tif(oldStream)This.CLog($T(\"upb8::发现同时多次调用open\"),1);\n\t\t\t\t\t\n\t\t\t\t\tConnect(streamStore);\n\t\t\t\t\tok(); //只连接，因为AudioContext不一定在运行，无法知道是否有数据回调\n\t\t\t\t}else{\n\t\t\t\t\tfailCall($T(\"Q1GA::录音功能无效：无音频流\"));\n\t\t\t\t};\n\t\t\t},100);\n\t\t};\n\t\tvar f2=function(e){ //请求失败回调\n\t\t\tvar code=e.name||e.message||e.code+\":\"+e;\n\t\t\tvar tryMsg=\"\";\n\t\t\tif(callUmCount==1 && codeErr1(0,code)){ \n\t\t\t\ttryMsg=$T(\"KxE2::，将尝试禁用回声消除后重试\");\n\t\t\t}\n\t\t\tvar msg1=$T(\"xEQR::请求录音权限错误\"),msg2=$T(\"bDOG::无法录音：\");\n\t\t\tThis.CLog(msg1+tryMsg+\"|\"+e,(tryMsg||f2_e)?3:1,e);\n\t\t\t\n\t\t\tif(tryMsg){//重试\n\t\t\t\tf2_c=code; f2_e=e; //重试如果还出错 返回第一个错误\n\t\t\t\tcallUserMedia(1);\n\t\t\t}else if(f2_e){\n\t\t\t\tThis.CLog(msg1+\"|\"+f2_e,1,f2_e);\n\t\t\t\tcodeFail(f2_c,msg2+f2_e);\n\t\t\t}else{\n\t\t\t\tcodeFail(code,msg2+e);\n\t\t\t};\n\t\t};\n\t\t\n\t\tvar callUmCount=0,f2_c,f2_e;\n\t\tvar callUserMedia=function(retry){\n\t\t\tcallUmCount++;\n\t\t\tvar atsTxt=\"audioTrackSet\";\n\t\t\tvar t_AGC=\"autoGainControl\",t_AEC=\"echoCancellation\",t_ANS=\"noiseSuppression\";\n\t\t\tvar atsTxtJs=atsTxt+\":{\"+t_AEC+\",\"+t_ANS+\",\"+t_AGC+\"}\";\n\t\t\tvar trackSet=JSON.parse(ToJson(set[atsTxt]||true)); //true 跟 {} 兼容性？\n\t\t\tThis.CLog(\"open... \"+callUmCount+\" \"+atsTxt+\":\"+ToJson(trackSet));\n\t\t\t\n\t\t\tif(retry){ //回声消除有些浏览器可能导致无法打开录音，尝试明确禁用来保证能最基础的录\n\t\t\t\tif(typeof(trackSet)!=\"object\")trackSet={}; //默认true\n\t\t\t\ttrackSet[t_AGC]=false;\n\t\t\t\ttrackSet[t_AEC]=false;\n\t\t\t\ttrackSet[t_ANS]=false;\n\t\t\t};\n\t\t\t//这里指明采样率，虽然可以解决手机上MediaRecorder采样率16k的问题，是回声消除导致的只能获取到16k的流，禁用回声消除可恢复48k。(issues#230)而且会导致乱用音频输入设备，MediaStreamTrack的applyConstraints也无法修改采样率\n\t\t\t//trackSet[sampleRateTxt]=ctx[sampleRateTxt];\n\t\t\tif(trackSet[sampleRateTxt]){\n\t\t\t\tThis.CLog($T(\"IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象\",0,atsTxt+\".\"+sampleRateTxt),3);\n\t\t\t};\n\t\t\t\n\t\t\tvar mSet={audio:trackSet, video:set.videoTrackSet||false};\n\t\t\ttry{\n\t\t\t\tvar pro=Recorder.Scope[getUserMediaTxt](mSet,f1,f2);\n\t\t\t}catch(e){//不能设置trackSet就算了\n\t\t\t\tThis.CLog(getUserMediaTxt,3,e);\n\t\t\t\tmSet={audio:true, video:false};\n\t\t\t\tpro=Recorder.Scope[getUserMediaTxt](mSet,f1,f2);\n\t\t\t};\n\t\t\tThis.CLog(getUserMediaTxt+\"(\"+ToJson(mSet)+\") \"+CtxState(ctx)\n\t\t\t\t+$T(\"RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置\",0,atsTxtJs,atsTxt)\n\t\t\t\t+\"(\"+GitUrl+\") LM:\"+LM+\" UA:\"+navigator.userAgent);\n\t\t\tif(pro&&pro.then){\n\t\t\t\tpro.then(f1)[CatchTxt](f2); //fix 关键字，保证catch压缩时保持字符串形式\n\t\t\t};\n\t\t};\n\t\tcallUserMedia();\n\t}\n\t//关闭释放录音资源\n\t,close:function(call){\n\t\tcall=call||NOOP;\n\t\t\n\t\tvar This=this,streamStore=This._streamStore();\n\t\tThis._stop();\n\t\tvar sTag=\" stream:\"+This._streamTag;\n\t\t\n\t\tvar Lock=streamStore.Sync;\n\t\tThis._O=0;\n\t\tif(This._O_!=Lock.O){\n\t\t\t//唯一资源Stream的控制权已交给新对象，这里不能关闭。此处在每次都弹权限的浏览器内可能存在泄漏，新对象被拒绝权限可能不会调用close，忽略这种不处理\n\t\t\tThis.CLog($T(\"hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）\")+sTag,3);\n\t\t\tcall();\n\t\t\treturn;\n\t\t};\n\t\tLock.C++;//获得控制权\n\t\t\n\t\tDisconnect(streamStore);\n\t\t\n\t\tThis.CLog(\"close,\"+sTag);\n\t\tcall();\n\t}\n\t\n\t\n\t\n\t\n\t\n\t/*模拟一段录音数据，后面可以调用stop进行编码，需提供pcm数据[1,2,3...]，pcm的采样率*/\n\t,mock:function(pcmData,pcmSampleRate){\n\t\tvar This=this;\n\t\tThis._stop();//清理掉已有的资源\n\t\t\n\t\tThis.isMock=1;\n\t\tThis.mockEnvInfo=null;\n\t\tThis.buffers=[pcmData];\n\t\tThis.recSize=pcmData.length;\n\t\tThis._setSrcSR(pcmSampleRate);\n\t\tThis._streamTag=\"mock\";\n\t\treturn This;\n\t}\n\t,_setSrcSR:function(sampleRate){\n\t\tvar This=this,set=This.set;\n\t\tvar setSr=set[sampleRateTxt];\n\t\tif(setSr>sampleRate){\n\t\t\tset[sampleRateTxt]=sampleRate;\n\t\t}else{ setSr=0 }\n\t\tThis[srcSampleRateTxt]=sampleRate;\n\t\tThis.CLog(srcSampleRateTxt+\": \"+sampleRate+\" set.\"+sampleRateTxt+\": \"+set[sampleRateTxt]+(setSr?\" \"+$T(\"UHvm::忽略\")+\": \"+setSr:\"\"), setSr?3:0);\n\t}\n\t,envCheck:function(envInfo){//平台环境下的可用性检查，任何时候都可以调用检查，返回errMsg:\"\"正常，\"失败原因\"\n\t\t//envInfo={envName:\"H5\",canProcess:true}\n\t\tvar errMsg,This=this,set=This.set;\n\t\t\n\t\t//检测CPU的数字字节序，TypedArray字节序是个迷，直接拒绝罕见的大端模式，因为找不到这种CPU进行测试\n\t\tvar tag=\"CPU_BE\";\n\t\tif(!errMsg && !Recorder[tag] && typeof Int8Array==\"function\" && !new Int8Array(new Int32Array([1]).buffer)[0]){\n\t\t\tTraffic(tag); //如果开启了流量统计，这里将发送一个图片请求\n\t\t\terrMsg=$T(\"Essp::不支持{1}架构\",0,tag);\n\t\t};\n\t\t\n\t\t//编码器检查环境下配置是否可用\n\t\tif(!errMsg){\n\t\t\tvar type=set.type,hasFn=This[type+\"_envCheck\"];\n\t\t\tif(set.takeoffEncodeChunk){//需要实时编码返回数据，此时需要检查环境是否有实时特性、和是否可实时编码\n\t\t\t\tif(!hasFn){\n\t\t\t\t\terrMsg=$T(\"2XBl::{1}类型不支持设置takeoffEncodeChunk\",0,type)+(This[type]?\"\":$T(\"LG7e::(未加载编码器)\"));\n\t\t\t\t}else if(!envInfo.canProcess){\n\t\t\t\t\terrMsg=$T(\"7uMV::{1}环境不支持实时处理\",0,envInfo.envName);\n\t\t\t\t};\n\t\t\t};\n\t\t\t\n\t\t\tif(!errMsg && hasFn){//编码器已实现环境检查\n\t\t\t\terrMsg=This[type+\"_envCheck\"](envInfo,set);\n\t\t\t};\n\t\t};\n\t\t\n\t\treturn errMsg||\"\";\n\t}\n\t,envStart:function(mockEnvInfo,sampleRate){//平台环境相关的start调用\n\t\tvar This=this,set=This.set;\n\t\tThis.isMock=mockEnvInfo?1:0;//非H5环境需要启用mock，并提供envCheck需要的环境信息\n\t\tThis.mockEnvInfo=mockEnvInfo;\n\t\tThis.buffers=[];//数据缓冲\n\t\tThis.recSize=0;//数据大小\n\t\tif(mockEnvInfo){\n\t\t\tThis._streamTag=\"env$\"+mockEnvInfo.envName;\n\t\t};\n\t\t\n\t\tThis.state=1;//运行状态，0未录音 1录音中 2暂停 3等待ctx激活\n\t\tThis.envInLast=0;//envIn接收到最后录音内容的时间\n\t\tThis.envInFirst=0;//envIn接收到的首个录音内容的录制时间\n\t\tThis.envInFix=0;//补偿的总时间\n\t\tThis.envInFixTs=[];//补偿计数列表\n\t\t\n\t\t//engineCtx需要提前确定最终的采样率\n\t\tThis._setSrcSR(sampleRate);\n\t\t\n\t\tThis.engineCtx=0;\n\t\t//此类型有边录边转码(Worker)支持\n\t\tif(This[set.type+\"_start\"]){\n\t\t\tvar engineCtx=This.engineCtx=This[set.type+\"_start\"](set);\n\t\t\tif(engineCtx){\n\t\t\t\tengineCtx.pcmDatas=[];\n\t\t\t\tengineCtx.pcmSize=0;\n\t\t\t};\n\t\t};\n\t}\n\t,envResume:function(){//和平台环境无关的恢复录音\n\t\t//重新开始计数\n\t\tthis.envInFixTs=[];\n\t}\n\t,envIn:function(pcm,sum){//和平台环境无关的pcm[Int16]输入\n\t\tvar This=this,set=This.set,engineCtx=This.engineCtx;\n\t\tif(This.state!=1){\n\t\t\tif(!This.state)This.CLog(\"envIn at state=0\",3);\n\t\t\treturn;\n\t\t};\n\t\tvar bufferSampleRate=This[srcSampleRateTxt];\n\t\tvar size=pcm.length;\n\t\tvar powerLevel=Recorder.PowerLevel(sum,size);\n\t\t\n\t\tvar buffers=This.buffers;\n\t\tvar bufferFirstIdx=buffers.length;//之前的buffer都是经过onProcess处理好的，不允许再修改\n\t\tbuffers.push(pcm);\n\t\t\n\t\t//有engineCtx时会被覆盖，这里保存一份\n\t\tvar buffersThis=buffers;\n\t\tvar bufferFirstIdxThis=bufferFirstIdx;\n\t\t\n\t\t//卡顿丢失补偿：因为设备很卡的时候导致H5接收到的数据量不够造成播放时候变速，结果比实际的时长要短，此处保证了不会变短，但不能修复丢失的音频数据造成音质变差。当前算法采用输入时间侦测下一帧是否需要添加补偿帧，需要(6次输入||超过1秒)以上才会开始侦测，如果滑动窗口内丢失超过1/3就会进行补偿\n\t\tvar now=Date.now();\n\t\tvar pcmTime=Math.round(size/bufferSampleRate*1000);\n\t\tThis.envInLast=now;\n\t\tif(This.buffers.length==1){//记下首个录音数据的录制时间\n\t\t\tThis.envInFirst=now-pcmTime;\n\t\t};\n\t\tvar envInFixTs=This.envInFixTs;\n\t\tenvInFixTs.splice(0,0,{t:now,d:pcmTime});\n\t\t//保留3秒的计数滑动窗口，另外超过3秒的停顿不补偿\n\t\tvar tsInStart=now,tsPcm=0;\n\t\tfor(var i=0;i<envInFixTs.length;i++){\n\t\t\tvar o=envInFixTs[i];\n\t\t\tif(now-o.t>3000){\n\t\t\t\tenvInFixTs.length=i;\n\t\t\t\tbreak;\n\t\t\t};\n\t\t\ttsInStart=o.t;\n\t\t\ttsPcm+=o.d;\n\t\t};\n\t\t//达到需要的数据量，开始侦测是否需要补偿\n\t\tvar tsInPrev=envInFixTs[1];\n\t\tvar tsIn=now-tsInStart;\n\t\tvar lost=tsIn-tsPcm;\n\t\tif( lost>tsIn/3 && (tsInPrev&&tsIn>1000 || envInFixTs.length>=6) ){\n\t\t\t//丢失过多，开始执行补偿\n\t\t\tvar addTime=now-tsInPrev.t-pcmTime;//距离上次输入丢失这么多ms\n\t\t\tif(addTime>pcmTime/5){//丢失超过本帧的1/5\n\t\t\t\tvar fixOpen=!set.disableEnvInFix;\n\t\t\t\tThis.CLog(\"[\"+now+\"]\"+i18n.get(fixOpen?$T(\"4Kfd::补偿{1}ms\",1):$T(\"bM5i::未补偿{1}ms\",1),[addTime]),3);\n\t\t\t\tThis.envInFix+=addTime;\n\t\t\t\t\n\t\t\t\t//用静默进行补偿\n\t\t\t\tif(fixOpen){\n\t\t\t\t\tvar addPcm=new Int16Array(addTime*bufferSampleRate/1000);\n\t\t\t\t\tsize+=addPcm.length;\n\t\t\t\t\tbuffers.push(addPcm);\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t\t\n\t\t\n\t\tvar sizeOld=This.recSize,addSize=size;\n\t\tvar bufferSize=sizeOld+addSize;\n\t\tThis.recSize=bufferSize;//此值在onProcess后需要修正，可能新数据被修改\n\t\t\n\t\t\n\t\t//此类型有边录边转码(Worker)支持，开启实时转码\n\t\tif(engineCtx){\n\t\t\t//转换成set的采样率\n\t\t\tvar chunkInfo=Recorder.SampleData(buffers,bufferSampleRate,set[sampleRateTxt],engineCtx.chunkInfo);\n\t\t\tengineCtx.chunkInfo=chunkInfo;\n\t\t\t\n\t\t\tsizeOld=engineCtx.pcmSize;\n\t\t\taddSize=chunkInfo.data.length;\n\t\t\tbufferSize=sizeOld+addSize;\n\t\t\tengineCtx.pcmSize=bufferSize;//此值在onProcess后需要修正，可能新数据被修改\n\t\t\t\n\t\t\tbuffers=engineCtx.pcmDatas;\n\t\t\tbufferFirstIdx=buffers.length;\n\t\t\tbuffers.push(chunkInfo.data);\n\t\t\tbufferSampleRate=chunkInfo[sampleRateTxt];\n\t\t};\n\t\t\n\t\tvar duration=Math.round(bufferSize/bufferSampleRate*1000);\n\t\tvar bufferNextIdx=buffers.length;\n\t\tvar bufferNextIdxThis=buffersThis.length;\n\t\t\n\t\t//允许异步处理buffer数据\n\t\tvar asyncEnd=function(){\n\t\t\t//重新计算size，异步的早已减去添加的，同步的需去掉本次添加的然后重新计算\n\t\t\tvar num=asyncBegin?0:-addSize;\n\t\t\tvar hasClear=buffers[0]==null;\n\t\t\tfor(var i=bufferFirstIdx;i<bufferNextIdx;i++){\n\t\t\t\tvar buffer=buffers[i];\n\t\t\t\tif(buffer==null){//已被主动释放内存，比如长时间实时传输录音时\n\t\t\t\t\thasClear=1;\n\t\t\t\t}else{\n\t\t\t\t\tnum+=buffer.length;\n\t\t\t\t\t\n\t\t\t\t\t//推入后台边录边转码\n\t\t\t\t\tif(engineCtx&&buffer.length){\n\t\t\t\t\t\tThis[set.type+\"_encode\"](engineCtx,buffer);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t\t\n\t\t\t//同步清理This.buffers，不管buffers到底清了多少个，buffersThis是使用不到的进行全清\n\t\t\tif(hasClear && engineCtx){\n\t\t\t\tvar i=bufferFirstIdxThis;\n\t\t\t\tif(buffersThis[0]){\n\t\t\t\t\ti=0;\n\t\t\t\t};\n\t\t\t\tfor(;i<bufferNextIdxThis;i++){\n\t\t\t\t\tbuffersThis[i]=null;\n\t\t\t\t};\n\t\t\t};\n\t\t\t\n\t\t\t//统计修改后的size，如果异步发生clear要原样加回来，同步的无需操作\n\t\t\tif(hasClear){\n\t\t\t\tnum=asyncBegin?addSize:0;\n\t\t\t\t\n\t\t\t\tbuffers[0]=null;//彻底被清理\n\t\t\t};\n\t\t\tif(engineCtx){\n\t\t\t\tengineCtx.pcmSize+=num;\n\t\t\t}else{\n\t\t\t\tThis.recSize+=num;\n\t\t\t};\n\t\t};\n\t\t//实时回调处理数据，允许修改或替换上次回调以来新增的数据 ，但是不允许修改已处理过的，不允许增删第一维数组 ，允许将第二维数组任意修改替换成空数组也可以\n\t\tvar asyncBegin=0,procTxt=\"rec.set.onProcess\";\n\t\ttry{\n\t\t\tasyncBegin=set.onProcess(buffers,powerLevel,duration,bufferSampleRate,bufferFirstIdx,asyncEnd);\n\t\t\tasyncBegin=asyncBegin===true;\n\t\t}catch(e){\n\t\t\t//此错误显示不要用CLog，这样控制台内相同内容不会重复打印\n\t\t\tconsole.error(procTxt+$T(\"gFUF::回调出错是不允许的，需保证不会抛异常\"),e);\n\t\t};\n\t\t\n\t\tvar slowT=Date.now()-now;\n\t\tif(slowT>10 && This.envInFirst-now>1000){ //1秒后开始onProcess性能监测\n\t\t\tThis.CLog(procTxt+$T(\"2ghS::低性能，耗时{1}ms\",0,slowT),3);\n\t\t};\n\t\t\n\t\tif(asyncBegin){\n\t\t\t//开启了异步模式，onProcess已接管buffers新数据，立即清空，避免出现未处理的数据\n\t\t\tvar hasClear=0;\n\t\t\tfor(var i=bufferFirstIdx;i<bufferNextIdx;i++){\n\t\t\t\tif(buffers[i]==null){//已被主动释放内存，比如长时间实时传输录音时 ，但又要开启异步模式，此种情况是非法的\n\t\t\t\t\thasClear=1;\n\t\t\t\t}else{\n\t\t\t\t\tbuffers[i]=new Int16Array(0);\n\t\t\t\t};\n\t\t\t};\n\t\t\t\n\t\t\tif(hasClear){\n\t\t\t\tThis.CLog($T(\"ufqH::未进入异步前不能清除buffers\"),3);\n\t\t\t}else{\n\t\t\t\t//还原size，异步结束后再统计仅修改后的size，如果发生clear要原样加回来\n\t\t\t\tif(engineCtx){\n\t\t\t\t\tengineCtx.pcmSize-=addSize;\n\t\t\t\t}else{\n\t\t\t\t\tThis.recSize-=addSize;\n\t\t\t\t};\n\t\t\t};\n\t\t}else{\n\t\t\tasyncEnd();\n\t\t};\n\t}\n\t\n\t\n\t\n\t\n\t//开始录音，需先调用open；只要open成功时，调用此方法是安全的，如果未open强行调用导致的内部错误将不会有任何提示，stop时自然能得到错误；注意：open和start至少有一个应当在用户操作（触摸、点击等）下进行调用，原因参考runningContext配置\n\t,start:function(){\n\t\tvar This=this;\n\t\t\n\t\tvar isOpen=1;\n\t\tif(This.set.sourceStream){//直接提供了流，仅判断是否调用了open\n\t\t\tif(!This.Stream){\n\t\t\t\tisOpen=0;\n\t\t\t}\n\t\t}else if(!Recorder.IsOpen()){//监测全局麦克风是否打开并且有效\n\t\t\tisOpen=0;\n\t\t};\n\t\tif(!isOpen){\n\t\t\tThis.CLog($T(\"6WmN::start失败：未open\"),1);\n\t\t\treturn;\n\t\t};\n\t\tvar ctx=This._streamCtx();\n\t\tThis.CLog($T(\"kLDN::start 开始录音，\")+CtxState(ctx)+\" stream:\"+This._streamTag);\n\t\t\n\t\tThis._stop();\n\t\tThis.envStart(null, ctx[sampleRateTxt]);\n\t\tThis.state=3;//0未录音 1录音中 2暂停 3等待ctx激活\n\t\t\n\t\t//检查open过程中stop是否已经调用过\n\t\tif(This._SO&&This._SO+1!=This._S){//上面调用过一次 _stop\n\t\t\t//open未完成就调用了stop，此种情况终止start。也应尽量避免出现此情况\n\t\t\tThis.CLog($T(\"Bp2y::start被中断\"),3);\n\t\t\treturn;\n\t\t};\n\t\tThis._SO=0;\n\t\t\n\t\tvar end=function(){\n\t\t\tif(This.state==3){\n\t\t\t\tThis.state=1;\n\t\t\t\tThis.resume();\n\t\t\t}\n\t\t};\n\t\tvar tag=\"AudioContext resume: \";\n\t\t\n\t\t//如果有数据回调，就不等待ctx resume\n\t\tvar stream=This._streamGet();\n\t\tstream._call[This.id]=function(){\n\t\t\tThis.CLog(tag+ctx.state+'|stream ok');\n\t\t\tend();\n\t\t};\n\t\tResumeCtx(ctx,function(runC){\n\t\t\trunC&&This.CLog(tag+\"wait...\");\n\t\t\treturn This.state==3;\n\t\t},function(runC){\n\t\t\trunC&&This.CLog(tag+ctx.state);\n\t\t\tend();\n\t\t},function(err){ //比较少见，可能对录音没有影响\n\t\t\tThis.CLog(tag+ctx.state+$T(\"upkE::，可能无法录音：\")+err,1);\n\t\t\tend();\n\t\t});\n\t}\n\t\n\t\n\t\n\t/*暂停录音*/\n\t,pause:function(){\n\t\tvar This=this,stream=This._streamGet();\n\t\tif(This.state){\n\t\t\tThis.state=2;\n\t\t\tThis.CLog(\"pause\");\n\t\t\tif(stream) delete stream._call[This.id];\n\t\t};\n\t}\n\t/*恢复录音*/\n\t,resume:function(){\n\t\tvar This=this,stream=This._streamGet();\n\t\tvar tag=\"resume\",tag3=tag+\"(wait ctx)\";\n\t\tif(This.state==3){ //start还在等ctx恢复\n\t\t\tThis.CLog(tag3);\n\t\t}else if(This.state){\n\t\t\tThis.state=1;\n\t\t\tThis.CLog(tag);\n\t\t\tThis.envResume();\n\t\t\t\n\t\t\tif(stream){\n\t\t\t\tstream._call[This.id]=function(pcm,sum){\n\t\t\t\t\tif(This.state==1){\n\t\t\t\t\t\tThis.envIn(pcm,sum);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t\tConnAlive(stream);//AudioWorklet只会在ctx激活后运行\n\t\t\t};\n\t\t\t\n\t\t\tvar ctx=This._streamCtx();\n\t\t\tif(ctx){ //AudioContext如果被暂停，尽量恢复\n\t\t\t\tResumeCtx(ctx,function(runC){\n\t\t\t\t\trunC&&This.CLog(tag3+\"...\");\n\t\t\t\t\treturn This.state==1;\n\t\t\t\t},function(runC){\n\t\t\t\t\trunC&&This.CLog(tag3+ctx.state);\n\t\t\t\t\tConnAlive(stream);\n\t\t\t\t},function(err){\n\t\t\t\t\tThis.CLog(tag3+ctx.state+\"[err]\"+err,1);\n\t\t\t\t});\n\t\t\t};\n\t\t};\n\t}\n\t\n\t\n\t\n\t\n\t,_stop:function(keepEngine){\n\t\tvar This=this,set=This.set;\n\t\tif(!This.isMock){\n\t\t\tThis._S++;\n\t\t};\n\t\tif(This.state){\n\t\t\tThis.pause();\n\t\t\tThis.state=0;\n\t\t};\n\t\tif(!keepEngine && This[set.type+\"_stop\"]){\n\t\t\tThis[set.type+\"_stop\"](This.engineCtx);\n\t\t\tThis.engineCtx=0;\n\t\t};\n\t}\n\t/*\n\t结束录音并返回录音数据blob对象\n\t\tTrue(blob,duration,mime)\n\t\t\tblob：录音数据audio/mp3|wav格式；默认是Blob对象，可设置rec.dataType=\"arraybuffer\"改成ArrayBuffer\n\t\t\tduration：录音时长，单位毫秒\n\t\t\tmime：\"auido/mp3\" blob数据的类型，方便ArrayBuffer时区分类型\n\t\tFalse(msg)\n\t\tautoClose:false 可选，是否自动调用close，默认为false\n\t*/\n\t,stop:function(True,False,autoClose){\n\t\tvar This=this,set=This.set,t1;\n\t\tvar envInMS=This.envInLast-This.envInFirst, envInLen=envInMS&&This.buffers.length; //可能未start\n\t\tThis.CLog($T(\"Xq4s::stop 和start时差:\")\n\t\t\t+(envInMS?envInMS+\"ms \"+$T(\"3CQP::补偿:\")+This.envInFix+\"ms\"\n\t\t\t\t+\" envIn:\"+envInLen+\" fps:\"+(envInLen/envInMS*1000).toFixed(1)\n\t\t\t:\"-\")+\" stream:\"+This._streamTag+\" (\"+GitUrl+\") LM:\"+LM);\n\t\t\n\t\tvar end=function(){\n\t\t\tThis._stop();//彻底关掉engineCtx\n\t\t\tif(autoClose){\n\t\t\t\tThis.close();\n\t\t\t};\n\t\t};\n\t\tvar err=function(msg){\n\t\t\tThis.CLog($T(\"u8JG::结束录音失败：\")+msg,1);\n\t\t\tFalse&&False(msg);\n\t\t\tend();\n\t\t};\n\t\tvar ok=function(blob,mime,duration){\n\t\t\tvar tBlob=\"blob\",tABuf=\"arraybuffer\",tDT=\"dataType\",tDDT=\"DefaultDataType\";\n\t\t\tvar dType=This[tDT]||Recorder[tDDT]||tBlob,dTag=tDT+\"=\"+dType;\n\t\t\tvar isAB=blob instanceof ArrayBuffer,dErr=0;\n\t\t\tvar dLen=isAB?blob.byteLength:blob.size;\n\t\t\tif(dType==tABuf){\n\t\t\t\tif(!isAB) dErr=1;\n\t\t\t}else if(dType==tBlob){\n\t\t\t\tif(typeof Blob!=\"function\"){\n\t\t\t\t\tdErr=$T.G(\"NonBrowser-1\",[dTag])+$T(\"1skY::，请设置{1}\",0,RecTxt+\".\"+tDDT+'=\"'+tABuf+'\"');\n\t\t\t\t}else{\n\t\t\t\t\tif(isAB) blob=new Blob([blob],{type:mime});\n\t\t\t\t\tif(!(blob instanceof Blob)) dErr=1;\n\t\t\t\t\tmime=blob.type||mime;\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tdErr=$T.G(\"NotSupport-1\",[dTag]);\n\t\t\t};\n\t\t\t\n\t\t\tThis.CLog($T(\"Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b\",0,Date.now()-t1,duration,dLen)+\" \"+dTag+\",\"+mime);\n\t\t\tif(dErr){\n\t\t\t\terr(dErr!=1?dErr:$T(\"Vkbd::{1}编码器返回的不是{2}\",0,set.type,dType)+\", \"+dTag);\n\t\t\t\treturn;\n\t\t\t};\n\t\t\tif(set.takeoffEncodeChunk){//接管了输出，此时blob长度为0\n\t\t\t\tThis.CLog($T(\"QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据\"),3);\n\t\t\t}else if(dLen<Math.max(50,duration/5)){//1秒小于0.2k？\n\t\t\t\terr($T(\"Sz2H::生成的{1}无效\",0,set.type));\n\t\t\t\treturn;\n\t\t\t};\n\t\t\t\n\t\t\tTrue&&True(blob,duration,mime);\n\t\t\tend();\n\t\t};\n\t\tif(!This.isMock){\n\t\t\tvar isCtxWait=This.state==3;\n\t\t\tif(!This.state || isCtxWait){\n\t\t\t\terr($T(\"wf9t::未开始录音\")+(isCtxWait?$T(\"Dl2c::，开始录音前无用户交互导致AudioContext未运行\"):\"\"));\n\t\t\t\treturn;\n\t\t\t};\n\t\t};\n\t\tThis._stop(true); //将录音状态改成未录音\n\t\tvar size=This.recSize;\n\t\tif(!size){\n\t\t\terr($T(\"Ltz3::未采集到录音\"));\n\t\t\treturn;\n\t\t};\n\t\tif(!This[set.type]){\n\t\t\terr($T(\"xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载\",0,set.type,RecTxt));\n\t\t\treturn;\n\t\t};\n\t\t\n\t\t//环境配置检查，此处仅针对mock调用，因为open已经检查过了\n\t\tif(This.isMock){\n\t\t\tvar checkMsg=This.envCheck(This.mockEnvInfo||{envName:\"mock\",canProcess:false});//没有提供环境信息的mock时没有onProcess回调\n\t\t\tif(checkMsg){\n\t\t\t\terr($T(\"AxOH::录音错误：\")+checkMsg);\n\t\t\t\treturn;\n\t\t\t};\n\t\t};\n\t\t\n\t\t//此类型有边录边转码(Worker)支持\n\t\tvar engineCtx=This.engineCtx;\n\t\tif(This[set.type+\"_complete\"]&&engineCtx){\n\t\t\tvar duration=Math.round(engineCtx.pcmSize/set[sampleRateTxt]*1000);//采用后的数据长度和buffers的长度可能微小的不一致，是采样率连续转换的精度问题\n\t\t\t\n\t\t\tt1=Date.now();\n\t\t\tThis[set.type+\"_complete\"](engineCtx,function(blob,mime){\n\t\t\t\tok(blob,mime,duration);\n\t\t\t},err);\n\t\t\treturn;\n\t\t};\n\t\t\n\t\t//标准UI线程转码，调整采样率\n\t\tt1=Date.now();\n\t\tif(!This.buffers[0]){\n\t\t\terr($T(\"xkKd::音频buffers被释放\"));\n\t\t\treturn;\n\t\t};\n\t\tvar chunk=Recorder.SampleData(This.buffers,This[srcSampleRateTxt],set[sampleRateTxt]);\n\t\t\n\t\tset[sampleRateTxt]=chunk[sampleRateTxt];\n\t\tvar res=chunk.data;\n\t\tvar duration=Math.round(res.length/set[sampleRateTxt]*1000);\n\t\t\n\t\tThis.CLog($T(\"CxeT::采样:{1} 花:{2}ms\",0,size+\"->\"+res.length,Date.now()-t1));\n\t\t\n\t\tsetTimeout(function(){\n\t\t\tt1=Date.now();\n\t\t\tThis[set.type](res,function(blob,mime){\n\t\t\t\tok(blob,mime,duration);\n\t\t\t},function(msg){\n\t\t\t\terr(msg);\n\t\t\t});\n\t\t});\n\t}\n\n};\n\n\n\n\n\n//=======从WebM字节流中提取pcm数据，提取成功返回Float32Array，失败返回null||-1=====\nvar WebM_Extract=function(inBytes, scope){\n\tif(!scope.pos){\n\t\tscope.pos=[0]; scope.tracks={}; scope.bytes=[];\n\t};\n\tvar tracks=scope.tracks, position=[scope.pos[0]];\n\tvar endPos=function(){ scope.pos[0]=position[0] };\n\t\n\tvar sBL=scope.bytes.length;\n\tvar bytes=new Uint8Array(sBL+inBytes.length);\n\tbytes.set(scope.bytes); bytes.set(inBytes,sBL);\n\tscope.bytes=bytes;\n\t\n\t//先读取文件头和Track信息\n\tif(!scope._ht){\n\t\treadMatroskaVInt(bytes, position);//EBML Header\n\t\treadMatroskaBlock(bytes, position);//跳过EBML Header内容\n\t\tif(!BytesEq(readMatroskaVInt(bytes, position), [0x18,0x53,0x80,0x67])){\n\t\t\treturn;//未识别到Segment\n\t\t}\n\t\treadMatroskaVInt(bytes, position);//跳过Segment长度值\n\t\twhile(position[0]<bytes.length){\n\t\t\tvar eid0=readMatroskaVInt(bytes, position);\n\t\t\tvar bytes0=readMatroskaBlock(bytes, position);\n\t\t\tvar pos0=[0],audioIdx=0;\n\t\t\tif(!bytes0)return;//数据不全，等待缓冲\n\t\t\t//Track完整数据，循环读取TrackEntry\n\t\t\tif(BytesEq(eid0, [0x16,0x54,0xAE,0x6B])){\n\t\t\t\twhile(pos0[0]<bytes0.length){\n\t\t\t\t\tvar eid1=readMatroskaVInt(bytes0, pos0);\n\t\t\t\t\tvar bytes1=readMatroskaBlock(bytes0, pos0);\n\t\t\t\t\tvar pos1=[0],track={channels:0,sampleRate:0};\n\t\t\t\t\tif(BytesEq(eid1, [0xAE])){//TrackEntry\n\t\t\t\t\t\twhile(pos1[0]<bytes1.length){\n\t\t\t\t\t\t\tvar eid2=readMatroskaVInt(bytes1, pos1);\n\t\t\t\t\t\t\tvar bytes2=readMatroskaBlock(bytes1, pos1);\n\t\t\t\t\t\t\tvar pos2=[0];\n\t\t\t\t\t\t\tif(BytesEq(eid2, [0xD7])){//Track Number\n\t\t\t\t\t\t\t\tvar val=BytesInt(bytes2);\n\t\t\t\t\t\t\t\ttrack.number=val;\n\t\t\t\t\t\t\t\ttracks[val]=track;\n\t\t\t\t\t\t\t}else if(BytesEq(eid2, [0x83])){//Track Type\n\t\t\t\t\t\t\t\tvar val=BytesInt(bytes2);\n\t\t\t\t\t\t\t\tif(val==1) track.type=\"video\";\n\t\t\t\t\t\t\t\telse if(val==2) {\n\t\t\t\t\t\t\t\t\ttrack.type=\"audio\";\n\t\t\t\t\t\t\t\t\tif(!audioIdx) scope.track0=track;\n\t\t\t\t\t\t\t\t\ttrack.idx=audioIdx++;\n\t\t\t\t\t\t\t\t}else track.type=\"Type-\"+val;\n\t\t\t\t\t\t\t}else if(BytesEq(eid2, [0x86])){//Track Codec\n\t\t\t\t\t\t\t\tvar str=\"\";\n\t\t\t\t\t\t\t\tfor(var i=0;i<bytes2.length;i++){\n\t\t\t\t\t\t\t\t\tstr+=String.fromCharCode(bytes2[i]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\ttrack.codec=str;\n\t\t\t\t\t\t\t}else if(BytesEq(eid2, [0xE1])){\n\t\t\t\t\t\t\t\twhile(pos2[0]<bytes2.length){//循环读取 Audio 属性\n\t\t\t\t\t\t\t\t\tvar eid3=readMatroskaVInt(bytes2, pos2);\n\t\t\t\t\t\t\t\t\tvar bytes3=readMatroskaBlock(bytes2, pos2);\n\t\t\t\t\t\t\t\t\t//采样率、位数、声道数\n\t\t\t\t\t\t\t\t\tif(BytesEq(eid3, [0xB5])){\n\t\t\t\t\t\t\t\t\t\tvar val=0,arr=new Uint8Array(bytes3.reverse()).buffer;\n\t\t\t\t\t\t\t\t\t\tif(bytes3.length==4) val=new Float32Array(arr)[0];\n\t\t\t\t\t\t\t\t\t\telse if(bytes3.length==8) val=new Float64Array(arr)[0];\n\t\t\t\t\t\t\t\t\t\telse CLog(\"WebM Track !Float\",1,bytes3);\n\t\t\t\t\t\t\t\t\t\ttrack[sampleRateTxt]=Math.round(val);\n\t\t\t\t\t\t\t\t\t}else if(BytesEq(eid3, [0x62,0x64])) track.bitDepth=BytesInt(bytes3);\n\t\t\t\t\t\t\t\t\telse if(BytesEq(eid3, [0x9F])) track.channels=BytesInt(bytes3);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tscope._ht=1;\n\t\t\t\tCLog(\"WebM Tracks\",tracks);\n\t\t\t\tendPos();\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t//校验音频参数信息，如果不符合代码要求，统统拒绝处理\n\tvar track0=scope.track0;\n\tif(!track0)return;\n\tvar trackSR=track0[sampleRateTxt]; scope.webmSR=trackSR;\n\tif(track0.bitDepth==16 && /FLOAT/i.test(track0.codec)){\n\t\ttrack0.bitDepth=32; //chrome v66 实际为浮点数\n\t\tCLog(\"WebM 16->32 bit\",3);\n\t}\n\tif(trackSR<8000 || track0.bitDepth!=32 || track0.channels<1 || !/(\\b|_)PCM\\b/i.test(track0.codec)){\n\t\tscope.bytes=[];//格式非预期 无法处理，清空缓冲数据\n\t\tif(!scope.bad)CLog(\"WebM Track Unexpected\",3,scope);\n\t\tscope.bad=1;\n\t\treturn -1;\n\t}\n\t\n\t//循环读取Cluster内的SimpleBlock\n\tvar datas=[],dataLen=0;\n\twhile(position[0]<bytes.length){\n\t\tvar eid1=readMatroskaVInt(bytes, position);\n\t\tvar bytes1=readMatroskaBlock(bytes, position);\n\t\tif(!bytes1)break;//数据不全，等待缓冲\n\t\tif(BytesEq(eid1, [0xA3])){//SimpleBlock完整数据\n\t\t\tvar trackNo=bytes1[0]&0xf;\n\t\t\tvar track=tracks[trackNo];\n\t\t\tif(!track){//不可能没有，数据出错？\n\t\t\t\tCLog(\"WebM !Track\"+trackNo,1,tracks);\n\t\t\t\treturn -1; //可能无法继续\n\t\t\t}else if(track.idx===0){\n\t\t\t\tvar u8arr=new Uint8Array(bytes1.length-4);\n\t\t\t\tfor(var i=4;i<bytes1.length;i++){\n\t\t\t\t\tu8arr[i-4]=bytes1[i];\n\t\t\t\t}\n\t\t\t\tdatas.push(u8arr); dataLen+=u8arr.length;\n\t\t\t}\n\t\t}\n\t\tendPos();\n\t}\n\t\n\tif(dataLen){\n\t\tvar more=new Uint8Array(bytes.length-scope.pos[0]);\n\t\tmore.set(bytes.subarray(scope.pos[0]));\n\t\tscope.bytes=more; //清理已读取了的缓冲数据\n\t\tscope.pos[0]=0;\n\t\t\n\t\tvar u8arr=new Uint8Array(dataLen); //已获取的音频数据\n\t\tfor(var i=0,i2=0;i<datas.length;i++){\n\t\t\tu8arr.set(datas[i],i2);\n\t\t\ti2+=datas[i].length;\n\t\t}\n\t\tvar arr=new Float32Array(u8arr.buffer);\n\t\t\n\t\tif(track0.channels>1){//多声道，提取一个声道\n\t\t\tvar arr2=[];\n\t\t\tfor(var i=0;i<arr.length;){\n\t\t\t\tarr2.push(arr[i]);\n\t\t\t\ti+=track0.channels;\n\t\t\t}\n\t\t\tarr=new Float32Array(arr2);\n\t\t};\n\t\treturn arr;\n\t}\n};\n//两个字节数组内容是否相同\nvar BytesEq=function(bytes1,bytes2){\n\tif(!bytes1 || bytes1.length!=bytes2.length) return false;\n\tif(bytes1.length==1) return bytes1[0]==bytes2[0];\n\tfor(var i=0;i<bytes1.length;i++){\n\t\tif(bytes1[i]!=bytes2[i]) return false;\n\t}\n\treturn true;\n};\n//字节数组BE转成int数字\nvar BytesInt=function(bytes){\n\tvar s=\"\";//0-8字节，js位运算只支持4字节\n\tfor(var i=0;i<bytes.length;i++){var n=bytes[i];s+=(n<16?\"0\":\"\")+n.toString(16)};\n\treturn parseInt(s,16)||0;\n};\n//读取一个可变长数值字节数组\nvar readMatroskaVInt=function(arr,pos,trim){\n\tvar i=pos[0];\n\tif(i>=arr.length)return;\n\tvar b0=arr[i],b2=(\"0000000\"+b0.toString(2)).substr(-8);\n\tvar m=/^(0*1)(\\d*)$/.exec(b2);\n\tif(!m)return;\n\tvar len=m[1].length, val=[];\n\tif(i+len>arr.length)return;\n\tfor(var i2=0;i2<len;i2++){ val[i2]=arr[i]; i++; }\n\tif(trim) val[0]=parseInt(m[2]||'0',2);\n\tpos[0]=i;\n\treturn val;\n};\n//读取一个自带长度的内容字节数组\nvar readMatroskaBlock=function(arr,pos){\n\tvar lenVal=readMatroskaVInt(arr,pos,1);\n\tif(!lenVal)return;\n\tvar len=BytesInt(lenVal);\n\tvar i=pos[0], val=[];\n\tif(len<0x7FFFFFFF){ //超大值代表没有长度\n\t\tif(i+len>arr.length)return;\n\t\tfor(var i2=0;i2<len;i2++){ val[i2]=arr[i]; i++; }\n\t}\n\tpos[0]=i;\n\treturn val;\n};\n//=====End WebM读取=====\n\n\n//=====i18n 简版国际化多语言支持=====\nvar i18n=Recorder.i18n={\n\tlang: \"zh-CN\" //默认中文\n\t,alias:{\"zh-CN\":\"zh\",\"en-US\":\"en\"} //别名配置，映射到一个语言实例\n\t,locales:{} //语言实例：zh:{key:\"text\"}\n\t,data:{} //各种数据，desc$xx：语言描述，rtl$xx：文本方向是否从右到左 rtl$zh:false rtl$ar:true\n\t//添加语言文本内容，set={lang:\"\",overwrite:true}，texts=[\"key:text{1}\",...]\n\t,put:function(set,texts){\n\t\tvar tag=RecTxt+\".i18n.put: \";\n\t\tvar overwrite=set.overwrite; overwrite=overwrite==null||overwrite;\n\t\tvar lang=set.lang; lang=i18n.alias[lang]||lang;\n\t\tif(!lang)throw new Error(tag+\"set.lang?\");\n\t\tvar locale=i18n.locales[lang];\n\t\tif(!locale){ locale={}; i18n.locales[lang]=locale };\n\t\tvar exp=/^([\\w\\-]+):/,m;\n\t\tfor(var i=0;i<texts.length;i++){\n\t\t\tvar v=texts[i]; m=exp.exec(v);\n\t\t\tif(!m){ CLog(tag+\"'key:'? \"+v,3,set); continue }\n\t\t\tvar key=m[1],v=v.substr(key.length+1);\n\t\t\tif(!overwrite && locale[key]) continue;\n\t\t\tlocale[key]=v;\n\t\t}\n\t}\n\t//获取key对应的文本，如果没有对应文本，将返回en的，en的也没有将返回zh的\n\t,get:function(/*key,args,lang*/){\n\t\treturn i18n.v_G.apply(null,arguments);\n\t}, v_G:function(key,args,lang){ //全局可重写get\n\t\targs=args||[];\n\t\tlang=lang||i18n.lang; lang=i18n.alias[lang]||lang;\n\t\tvar locale=i18n.locales[lang];\n\t\tvar val=locale&&locale[key]||\"\";\n\t\tif(!val&&lang!=\"zh\"){\n\t\t\tif(lang==\"en\")return i18n.v_G(key,args,\"zh\");\n\t\t\treturn i18n.v_G(key,args,\"en\");\n\t\t}\n\t\ti18n.lastLang=lang;\n\t\tif(val==\"=Empty\")return \"\";\n\t\treturn val.replace(/\\{(\\d+)(\\!?)\\}/g,function(v,a,b){\n\t\t\ta=+a||0; v=args[a-1];\n\t\t\tif(a<1 || a>args.length){ v=\"{?}\"; CLog(\"i18n[\"+key+\"] no {\"+a+\"}: \"+val,3) }\n\t\t\treturn b?\"\":v;\n\t\t});\n\t}\n\t/**返回一个国际化文本，返回的文本取决于i18n.lang。\n\t\t调用参数：T(\"key:[lang]:中文{1}\",\"[lang]:英文{1}\",...,0,\"args1\",\"args2\")，除了key:，其他都是可选的，文本如果在语言实例中不存在会push进去，第一个文本无lang时默认zh，第二个无lang时默认en，文本中用{1-n}来插入args\n\t\t第一个args前面这个参数必须是0；也可以不提供args，这个参数填args的数量，此时不返回文本，只返回key，再用i18n.get提供参数获取文本\n\t\t本函数调用，第一个文本需中文，调用尽量简单，不要换行，方便后续自动提取翻译列表\n\t\targs如果旧的参数位置发生了变更，应当使用新的key，让旧的翻译失效，增加args无需更换key\n\t\tkey的生成使用随机字符串，不同源码里面可以搞个不同前缀:\n\t\t\ts=\"\";L=4; for(var i=0,n;i<L;i++){ n=~~(Math.random()*62);s+=n<10?n:String.fromCharCode(n<36?55+n:61+n); }; s\n\t**/\n\t,$T:function(){\n\t\treturn i18n.v_T.apply(null,arguments);\n\t} ,v_T:function(){ //全局可重写$T\n\t\tvar a=arguments,key=\"\",args=[],isArgs=0,tag=RecTxt+\".i18n.$T:\";\n\t\tvar exp=/^([\\w\\-]*):/,m;\n\t\tfor(var i=0;i<a.length;i++){\n\t\t\tvar v=a[i];\n\t\t\tif(i==0){\n\t\t\t\tm=exp.exec(v); key=m&&m[1];\n\t\t\t\tif(!key)throw new Error(tag+\"0 'key:'?\");\n\t\t\t\tv=v.substr(key.length+1);\n\t\t\t}\n\t\t\tif(isArgs===-1) args.push(v);\n\t\t\telse if(isArgs) throw new Error(tag+\" bad args\");\n\t\t\telse if(v===0) isArgs=-1;\n\t\t\telse if(IsNum(v)){\n\t\t\t\tif(v<1) throw new Error(tag+\" bad args\");\n\t\t\t\tisArgs=v;\n\t\t\t}else{\n\t\t\t\tvar lang=i==1?\"en\":i?\"\":\"zh\";\n\t\t\t\tm=exp.exec(v); if(m){ lang=m[1]||lang; v=v.substr(m[1].length+1); }\n\t\t\t\tif(!m || !lang)throw new Error(tag+i+\" 'lang:'?\");\n\t\t\t\ti18n.put({lang:lang,overwrite:false},[key+\":\"+v]);\n\t\t\t}\n\t\t}\n\t\tif(!key)return \"\";\n\t\tif(isArgs>0)return key;\n\t\treturn i18n.v_G(key,args);\n\t}\n};\nvar $T=i18n.$T; $T.G=i18n.get;\n//预定义文本\n$T(\"NonBrowser-1::非浏览器环境，不支持{1}\",1);\n$T(\"IllegalArgs-1::参数错误：{1}\",1);\n$T(\"NeedImport-2::调用{1}需要先导入{2}\",2);\n$T(\"NotSupport-1::不支持：{1}\",1);\n//=====End i18n=====\n\n\n\n//流量统计用1像素图片地址，设置为空将不参与统计\nRecorder.TrafficImgUrl=\"//ia.51.la/go1?id=20469973&pvFlag=1\";\nvar Traffic=Recorder.Traffic=function(report){\n\tif(!isBrowser)return;\n\treport=report?\"/\"+RecTxt+\"/Report/\"+report:\"\";\n\tvar imgUrl=Recorder.TrafficImgUrl;\n\tif(imgUrl){\n\t\tvar data=Recorder.Traffic;\n\t\tvar m=/^(https?:..[^\\/#]*\\/?)[^#]*/i.exec(location.href)||[];\n\t\tvar host=(m[1]||\"http://file/\");\n\t\tvar idf=(m[0]||host)+report;\n\t\t\n\t\tif(imgUrl.indexOf(\"//\")==0){\n\t\t\t//给url加上http前缀，如果是file协议下，不加前缀没法用\n\t\t\tif(/^https:/i.test(idf)){\n\t\t\t\timgUrl=\"https:\"+imgUrl;\n\t\t\t}else{\n\t\t\t\timgUrl=\"http:\"+imgUrl;\n\t\t\t};\n\t\t};\n\t\tif(report){\n\t\t\timgUrl=imgUrl+\"&cu=\"+encodeURIComponent(host+report);\n\t\t};\n\t\t\n\t\tif(!data[idf]){\n\t\t\tdata[idf]=1;\n\t\t\t\n\t\t\tvar img=new Image();\n\t\t\timg.src=imgUrl;\n\t\t\tCLog(\"Traffic Analysis Image: \"+(report||RecTxt+\".TrafficImgUrl=\"+Recorder.TrafficImgUrl));\n\t\t};\n\t};\n};\n\n\n\nif(WRec2){\n\tCLog($T(\"8HO5::覆盖导入{1}\",0,RecTxt),1);\n\tWRec2.Destroy();\n};\nExport[RecTxt]=Recorder;\n\n}));"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,SAAS,SAAQ;AACjB,UAAI,UAAQ,OAAO,UAAQ,YAAY,CAAC,CAAC,OAAO;AAChD,UAAI,MAAI,UAAQ,SAAO;AACvB,cAAQ,KAAI,OAAO;AAEnB,UAAG,OAAO,UAAS,cAAc,OAAO,KAAI;AAC3C,eAAO,WAAU;AAChB,iBAAO,IAAI;AAAA,QACZ,CAAC;AAAA,MACF;AAAC;AACD,UAAG,OAAO,UAAS,YAAY,OAAO,SAAQ;AAC7C,eAAO,UAAQ,IAAI;AAAA,MACpB;AAAC;AAAA,IACF,GAAE,SAAS,QAAO,WAAU;AAC5B;AAEA,UAAI,OAAK,WAAU;AAAA,MAAC;AACpB,UAAI,QAAM,SAAS,GAAE;AAAC,eAAO,OAAO,KAAG;AAAA,MAAQ;AAC/C,UAAI,SAAO,SAAS,GAAE;AAAC,eAAO,KAAK,UAAU,CAAC;AAAA,MAAC;AAE/C,UAAI,WAAS,SAAS,KAAI;AACzB,eAAO,IAAI,OAAO,GAAG;AAAA,MACtB;AACA,UAAI,KAAG,SAAS,KAAG;AACnB,UAAI,SAAO;AACX,UAAI,SAAO;AACX,UAAI,kBAAgB;AACpB,UAAI,mBAAiB;AACrB,UAAI,gBAAc;AAClB,UAAI,aAAW;AACf,UAAI,WAAS;AAEb,UAAI,QAAM,OAAO,MAAM;AACvB,UAAG,SAAO,MAAM,MAAI,IAAG;AACtB,cAAM,KAAK,MAAM,KAAK,GAAG,iBAAgB,GAAE,MAAM,GAAE,CAAC;AACpD;AAAA,MACD;AAAC;AAID,eAAS,SAAO,WAAU;AACzB,YAAI,SAAO,SAAS;AACpB,YAAG,QAAO;AACT,cAAI,SAAO,QAAQ,MAAM,GAAG,QAAM,OAAO,CAAC;AAC1C,cAAG,OAAM;AACR,gBAAI,QAAM,MAAM;AAChB,mBAAO,SAAO,UAAQ,SAAO,MAAM;AAAA,UACpC;AAAC;AAAA,QACF;AAAC;AACD,eAAO;AAAA,MACR;AAMA,eAAS,aAAW;AAEpB,eAAS,UAAQ,WAAU;AAC1B,aAAK,SAAO,UAAU;AACtB,mBAAW;AAEX,iBAAQ,KAAK,aAAY;AACxB,sBAAY,CAAC,EAAE;AAAA,QAChB;AAAC;AAAA,MACF;AACA,UAAI,cAAY,CAAC;AAEjB,eAAS,cAAY,SAAS,KAAI,MAAK;AACtC,oBAAY,GAAG,IAAE;AAAA,MAClB;AAEA,eAAS,UAAQ,WAAU;AAC1B,YAAG,CAAC,UAAW,QAAO;AACtB,YAAI,QAAM,UAAU,gBAAc,CAAC;AACnC,YAAG,CAAC,MAAM,eAAe,GAAE;AAC1B,kBAAM;AACN,gBAAM,eAAe,MAAI,MAAM,eAAe,IAAE,MAAM,sBAAoB,MAAM,mBAAiB,MAAM;AAAA,QACxG;AAAC;AACD,YAAG,CAAC,MAAM,eAAe,GAAE;AAC1B,iBAAO;AAAA,QACR;AAAC;AACD,iBAAS,QAAM;AAEf,YAAG,CAAC,SAAS,WAAW,GAAE;AACzB,iBAAO;AAAA,QACR;AAAC;AACD,eAAO;AAAA,MACR;AAEA,eAAS,aAAW,SAAS,QAAO;AACnC,YAAG,CAAC,UAAW,QAAO;AACtB,YAAI,KAAG,OAAO;AACd,YAAG,CAAC,IAAG;AACN,eAAG,OAAO;AAAA,QACX;AAAC;AACD,YAAG,CAAC,IAAG;AACN,iBAAO;AAAA,QACR;AAAC;AAED,YAAI,MAAI,SAAS,KAAK,QAAM;AAC5B,YAAG,CAAC,KAAI;AAEP,gBAAI,SAAS,MAAI,IAAI,GAAG;AAAG,kBAAM;AACjC,mBAAS,UAAQ,SAAS,WAAS,CAAC;AAEpC,mBAAS,YAAY,OAAM,WAAU;AACpC,gBAAIA,OAAI,SAAS;AACjB,gBAAGA,QAAKA,KAAI,OAAM;AACjB,uBAASA,IAAG;AACZ,uBAAS,MAAI;AAAA,YACd;AAAC;AACD,gBAAI,MAAI,SAAS;AAAS,qBAAS,UAAQ,CAAC;AAC5C,qBAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,IAAI,UAAS,IAAI,CAAC,CAAC;AAAA,UAC7C,CAAC;AAAA,QACF;AAAC;AACD,YAAG,UAAU,IAAI,OAAM;AACtB,cAAG,CAAC,OAAM;AACT,gBAAG,CAAC,IAAI,MAAO,UAAS,GAAG;AAC3B,kBAAI,IAAI,GAAG;AAAA,UACZ;AAAC;AACD,cAAI,QAAM;AACV,mBAAS,QAAQ,KAAK,GAAG;AAAA,QAC1B;AAAC;AACD,eAAO;AAAA,MACR;AAEA,eAAS,cAAY,SAAS,KAAI;AACjC,YAAG,OAAO,IAAI,OAAM;AACnB,mBAAS,GAAG;AACZ,cAAI,MAAI,SAAS,WAAS,CAAC,GAAE,IAAE,IAAI;AACnC,mBAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,KAAI;AAC5B,gBAAG,IAAI,CAAC,KAAG,KAAI;AAAE,kBAAI,OAAO,GAAE,CAAC;AAAG;AAAA,YAAO;AAAA,UAC1C;AACA,eAAK,GAAG,+BAA8B,GAAE,IAAE,QAAM,IAAI,MAAM,GAAE,IAAI,SAAO,IAAE,CAAC;AAAA,QAC3E;AAAA,MACD;AACA,UAAI,WAAS,SAAS,KAAI;AACzB,YAAG,OAAO,IAAI,SAAS,CAAC,IAAI,MAAK;AAChC,cAAI,OAAK;AACT,cAAG,IAAI,SAAO,UAAS;AACtB,gBAAG;AAAE,kBAAI,MAAM;AAAA,YAAE,SAAO,GAAE;AAAE,mBAAK,iBAAgB,GAAE,CAAC;AAAA,YAAE;AAAA,UACvD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,YAAU,SAAS,YAAU,SAAS,KAAI,OAAM,MAAK,OAAM;AAC9D,YAAI,QAAM,GAAE,SAAO,GAAE,SAAO,GAAE,OAAK,GAAE,KAAG,iBAAgB,MAAI;AAC5D,YAAI,MAAI,SAAS,KAAI,IAAG;AACvB,cAAG,QAAO;AAAE,iBAAK;AAAA,UAAE;AACnB,cAAG,CAAC,OAAM;AAAE,oBAAM;AACjB,mBAAK,MAAM,KAAI,IAAI;AACnB,kBAAI,KAAK,IAAI;AAAA,UACd;AACA,cAAG,IAAG;AACL,gBAAG,CAAC,IAAI,SAAS,IAAI,QAAM,EAAE,EAAG,KAAI,QAAM,EAAE,EAAE,eAAc,GAAG;AAC/D,gBAAI,QAAM;AAAG,qBAAO;AAAA,UACrB;AAAA,QACD;AACA,YAAI,OAAK,SAAS,KAAI;AACrB,cAAG,OAAO,OAAQ;AAAQ,mBAAO,MAAI,IAAE;AACvC,cAAI,QAAM,CAAC,SAAQ,aAAY,WAAU,cAAa,UAAU;AAChE,mBAAQ,IAAE,GAAE,IAAE,MAAM,QAAO;AAC1B,oBAAQ,MAAI,QAAM,YAAU,EAAE,EAAE,MAAM,CAAC,GAAE,KAAI,IAAI;AAAA,QACnD;AACA,YAAI,MAAI,WAAU;AACjB,cAAI,OAAK,IAAI,OAAM,QAAM,SAAS,IAAI;AACtC,cAAG,CAAC,SAAS,CAAC,MAAM,QAAM,EAAE,OAAK,IAAI,EAAE,QAAO,IAAI;AAClD,cAAG,OAAM;AACR,gBAAG,OAAO,MAAK,MAAI,QAAM,MAAK,CAAC;AAC/B,iBAAK,CAAC;AACN,gBAAI,OAAO,EAAE,KAAK,WAAU;AAC3B,kBAAG,OAAO,MAAK,MAAI,QAAM,IAAI,KAAK;AAClC,kBAAI,GAAE,CAAC;AAAA,YACR,CAAC,EAAE,QAAQ,EAAE,SAAS,GAAE;AACvB,mBAAK,MAAI,SAAQ,GAAE,CAAC;AACpB,kBAAG,CAAC,SAAS,IAAI,KAAK,GAAE;AACvB,oBAAI,EAAE,WAAS,OAAO;AAAA,cACvB;AAAA,YACD,CAAC;AAAA,UACF,WAAS,QAAM,UAAS;AACvB,gBAAG,UAAU,CAAC,IAAI,KAAK,MAAK,MAAI,QAAM,MAAK,CAAC;AAC5C,gBAAI,YAAY;AAAA,UACjB,OAAK;AAAE,gBAAI,GAAE,CAAC;AAAA,UAAE;AAAC;AAAA,QAClB;AACA,YAAI;AAAA,MACL;AACA,UAAI,WAAS,SAAS,WAAS,SAAS,GAAE;AACzC,eAAO,KAAG,eAAa,KAAG;AAAA,MAC3B;AACA,UAAI,WAAS,SAAS,KAAI;AACzB,YAAI,IAAE,IAAI,OAAM,MAAI,eAAa;AACjC,YAAG,SAAS,CAAC,EAAE,QAAK,GAAG,uIAAuI;AAC9J,eAAO;AAAA,MACR;AAIA,UAAI,oBAAkB;AACtB,eAAS,iBAAiB,IAAE;AAG5B,UAAI,uBAAqB;AACzB,eAAS,oBAAoB,IAAE;AAG/B,UAAI,UAAQ,SAAS,aAAY;AAChC,YAAI,aAAW,YAAY,cAAY,SAAS;AAEhD,YAAI,SAAO,YAAY;AACvB,YAAI,MAAI,OAAO,IAAI,QAAM,IAAI,aAAa,GAAG,UAAQ,CAAC;AAGtD,YAAI,SAAO,QAAQ,MAAM,GAAE,QAAM,OAAO,CAAC,GAAE,WAAS,MAAK,QAAM;AAC/D,YAAG,SAAS,MAAM,aAAY;AAC7B,qBAAS,MAAM,YAAY;AAC3B,cAAI,UAAQ,SAAS,aAAa;AAClC,cAAG,WAAW,WAAS,OAAM;AAC5B,oBAAM,GAAG,wGAAuG,GAAE,SAAQ,KAAK;AAAA,UAChI;AAAA,QACD;AACA,eAAO,MAAI;AACX,aAAK,QAAM,sBAAoB,OAAO,QAAQ,GAAG,QAAM,IAAE,CAAC;AAE1D,YAAI,YAAU,SAAS,MAAK;AAC3B,cAAI,QAAM,OAAO,KAAG,IAAI,wBAAwB,MAAM;AACtD,cAAI,UAAQ,IAAI,aAAY,UAAQ;AACpC,cAAG,IAAI,OAAO,GAAE;AACf,sBAAQ,OAAO,KAAG,IAAI,OAAO,EAAE;AAAA,UAChC;AAAC;AACD,gBAAM,QAAQ,IAAI;AAClB,eAAK,QAAQ,OAAO;AAAA,QACrB;AACA,YAAI,QAAO,WAAU,QAAO,WAAS;AACrC,YAAI,QAAM,OAAO;AAGjB,YAAI,YAAU,SAAS,YAAY,OAAM;AACxC,mBAAQ,MAAM,OAAM;AACnB,gBAAG,SAAO,OAAM;AACf,sBAAQ,QAAM;AACd,wBAAQ,SAAS,WAAW,CAAC,UAAU,GAAE,OAAM,OAAM,SAAQ,EAAC,MAAK,EAAC,CAAC;AACrE,kBAAI,MAAI,QAAQ;AAChB,kBAAI,MAAI,QAAQ;AAAA,YACjB,OAAK;AACJ,wBAAQ,CAAC;AACT,kBAAI,OAAK,WAAW;AAEpB,kBAAI,MAAI,IAAI,WAAW,IAAI;AAC3B,kBAAI,MAAI;AACR,uBAAQ,IAAE,GAAE,IAAE,MAAK,KAAI;AACtB,oBAAI,IAAE,KAAK,IAAI,IAAG,KAAK,IAAI,GAAE,WAAW,CAAC,CAAC,CAAC;AAC3C,oBAAE,IAAE,IAAE,IAAE,QAAO,IAAE;AACjB,oBAAI,CAAC,IAAE;AACP,uBAAK,KAAK,IAAI,CAAC;AAAA,cAChB;AAAC;AAAA,YACF;AAAC;AAED,qBAAQ,KAAK,OAAM;AAClB,oBAAM,CAAC,EAAE,KAAI,GAAG;AAAA,YACjB;AAAC;AAED;AAAA,UACD;AAAC;AAAA,QACF;AAEA,YAAI,kBAAgB;AACpB,YAAI,eAAa;AACjB,YAAI,kBAAgB,SAAO,MAAI;AAC/B,YAAI,UAAQ;AACZ,YAAI,mBAAiB;AACrB,YAAI,YAAU,mBAAiB;AAK/B,YAAI,QAAM,IAAI,yBAAuB,IAAI;AACzC,YAAI,YAAU,GAAG,mEAAkE,GAAE,YAAY;AACjG,YAAI,YAAU,WAAU;AACvB,sBAAU,OAAO,YAAU;AAC3B,qBAAW,MAAM;AACjB,eAAK,GAAG,yBAAwB,GAAE,eAAe,IAC/C,KAAK;AAAA,YAAI,SAAS,oBAAoB,IACtC,GAAG,wBAAuB,CAAC,IAC1B,GAAG,uBAAsB,CAAC;AAAA,YAC1B,CAAC,SAAO,MAAI,uBAAqB,SAAQ,YAAY;AAAA,UACvD,IAAE,WAAS,WAAU,CAAC;AAEvB,cAAI,UAAQ,OAAO,KAAG,MAAM,KAAK,KAAI,YAAW,GAAE,CAAC;AACnD,oBAAU,OAAO;AAEjB,kBAAQ,iBAAe,SAAS,GAAE;AACjC,gBAAI,MAAI,EAAE,YAAY,eAAe,CAAC;AACtC,sBAAU,KAAK,KAAK;AAAA,UACrB;AAAA,QACD;AAID,YAAI,cAAY,WAAU;AAEzB,mBAAO,OAAO,SAAO;AACrB,qBAAW,MAAM;AAEjB,sBAAU,OAAO,YAAU,CAAC,SAAS,SAAS,oBAAoB;AAClE,cAAI,SAAO,OAAO;AAClB,cAAG,EAAE,aAAa,IAAI,YAAY,KAAK,SAAQ;AAC9C,sBAAU;AACV;AAAA,UACD;AAAC;AACD,cAAI,WAAS,WAAU;AACtB,gBAAI,KAAG,SAAS,GAAE;AAAC,qBAAO,EAAE,SAAS,EAAE,QAAQ,mBAAkB,EAAE,EAAE,QAAQ,SAAQ,eAAe;AAAA,YAAC;AACrG,gBAAI,QAAM,WAAS,UAAQ;AAC1B,qBAAO,iBAAe,GAAG,SAAS,QAAO;AACxC,wBAAU,MAAM;AAChB,kBAAI,OAAK,MAAKC,cAAW,OAAO,iBAAiB;AACjD,mBAAK,aAAWA;AAChB,mBAAK,SAAO,IAAI,aAAaA,cAAW,CAAC;AACzC,mBAAK,MAAI;AACT,mBAAK,KAAK,YAAU,SAAS,GAAE;AAC9B,oBAAG,EAAE,KAAK,MAAK;AACd,uBAAK,OAAK;AACV,qBAAG,IAAI,eAAe;AAAA,gBACvB;AAAA,cACD;AACA,iBAAG,IAAI,kBAAkB,MAAM;AAAA,YAChC,CAAC;AAGD,qBAAO,aAAW,GAAG,SAAS,OAAM,GAAE,GAAE;AACvC,kBAAI,OAAK,MAAKA,cAAW,KAAK;AAC9B,kBAAI,SAAO,KAAK,QAAO,MAAI,KAAK;AAChC,uBAAO,MAAM,CAAC,KAAG,CAAC,GAAG,CAAC,KAAG,CAAC;AAC1B,kBAAG,MAAM,QAAO;AACf,uBAAO,IAAI,OAAM,GAAG;AACpB,uBAAK,MAAM;AAEX,oBAAI,MAAI,CAAC,EAAE,MAAIA,eAAYA;AAC3B,oBAAG,KAAI;AACN,uBAAK,KAAK,YAAY,EAAE,KAAK,OAAO,MAAM,GAAE,GAAG,EAAE,CAAC;AAElD,sBAAI,OAAK,OAAO,SAAS,KAAI,GAAG;AAChC,2BAAO,IAAI,aAAaA,cAAW,CAAC;AACpC,yBAAO,IAAI,IAAI;AACf,wBAAI,KAAK;AACT,uBAAK,SAAO;AAAA,gBACb;AACA,qBAAK,MAAI;AAAA,cACV;AACA,qBAAO,CAAC,KAAK;AAAA,YACd,CAAC;AACF,qBAAO,6BAEkB,UAAQ,QAAM,UAAQ,0BACtB,kBAAgB;AACzC,oBAAM,MAAM,QAAQ,UAAS,UAAU;AAEvC,mBAAO,iCAA+B,KAAK,SAAS,mBAAmB,KAAK,CAAC,CAAC;AAAA,UAC/E;AAEA,cAAI,SAAO,WAAU;AACpB,mBAAO,aAAa,OAAO;AAAA,UAC5B;AACA,cAAI,YAAU,OAAO,MAAI,WAAU;AAElC,gBAAG,WAAS,IAAG;AACd,2BAAa,MAAM;AACnB,uBAAO,WAAW,WAAU;AAC3B,yBAAO;AACP,oBAAG,OAAO,GAAE;AACX,uBAAK,GAAG,4BAA2B,GAAE,cAAa,eAAe,GAAE,CAAC;AACpE,2BAAO,UAAU;AAAA,gBAClB;AAAC;AAAA,cACF,GAAE,GAAG;AAAA,YACN;AAAC;AAAA,UACF;AACA,cAAI,aAAW,WAAU;AACxB,gBAAG,CAAC,OAAO,EAAE;AACb,gBAAI,OAAK,OAAO,KAAG,IAAI,OAAO,KAAK,SAAS;AAAA,cAC3C,kBAAiB,EAAC,WAAqB;AAAA,YACxC,CAAC;AACD,sBAAU,IAAI;AACd,iBAAK,KAAK,YAAU,SAAS,GAAE;AAC9B,kBAAG,QAAO;AACT,6BAAa,MAAM;AAAE,yBAAO;AAAA,cAC7B;AAAC;AACD,kBAAG,OAAO,GAAE;AACX,0BAAU,EAAE,KAAK,KAAK,KAAK;AAAA,cAC5B,WAAS,CAAC,WAAU;AACnB,qBAAK,GAAG,iBAAgB,GAAE,YAAY,GAAE,CAAC;AAAA,cAC1C;AAAC;AAAA,YACF;AACA,iBAAK,GAAG,oCAAmC,GAAE,cAAa,SAAO,MAAI,uBAAqB,UAAS,eAAe,IAAE,WAAS,WAAU,CAAC;AAAA,UACzI;AAGA,cAAI,QAAM,WAAU;AACnB,gBAAG,CAAC,OAAO,EAAE;AACb,gBAAG,IAAI,OAAO,GAAE;AACf,yBAAW;AACX;AAAA,YACD;AAAC;AACD,gBAAI,MAAI,SAAS;AACjB,gBAAI,YAAY,EAAE,UAAU,GAAG,EAAE,KAAK,SAAS,GAAE;AAChD,kBAAG,CAAC,OAAO,EAAE;AACb,kBAAI,OAAO,IAAE;AACb,yBAAW;AACX,kBAAG,QAAO;AACT,0BAAU;AAAA,cACX;AAAC;AAAA,YACF,CAAC,EAAE,QAAQ,EAAE,SAAS,GAAE;AACvB,mBAAK,eAAa,oBAAmB,GAAE,CAAC;AACxC,qBAAO,KAAG,UAAU;AAAA,YACrB,CAAC;AAAA,UACF;AACA,oBAAU,KAAI,WAAU;AAAE,mBAAO,OAAO;AAAA,UAAE,GAAG,OAAM,KAAK;AAAA,QACzD;AAIA,YAAI,WAAS,WAAU;AAEtB,cAAI,KAAG,OAAO,gBAAgB;AAC9B,cAAI,SAAO;AACX,cAAI,WAAS;AACb,mBAAO,OAAO,SAAO,SAAS,iBAAiB;AAE/C,cAAI,YAAU,MAAO,UAAU,GAAG,aAAc,GAAG,gBAAgB,QAAQ;AAC3E,qBAAS,YAAU,KAAG,GAAG,sBAAqB,GAAE,SAAS;AACzD,cAAG,CAAC,UAAU,CAAC,WAAU;AACxB,wBAAY;AACZ;AAAA,UACD;AAEA,cAAI,SAAO,WAAU;AACpB,mBAAO,UAAU,OAAO;AAAA,UACzB;AACA,iBAAO,MAAI,WAAU;AAEpB,gBAAG,WAAS,IAAG;AACd,2BAAa,MAAM;AACnB,uBAAO,WAAW,WAAU;AAE3B,oBAAG,OAAO,GAAE;AACX,uBAAK,GAAG,4BAA2B,GAAE,kBAAiB,YAAY,GAAE,CAAC;AACrE,8BAAY;AAAA,gBACb;AAAC;AAAA,cACF,GAAE,GAAG;AAAA,YACN;AAAC;AAAA,UACF;AAEA,cAAI,QAAM,OAAO,OAAO,EAAC,UAAS,SAAQ,GAAG,SAAS,kBAAkB;AACxE,cAAI,KAAG,OAAO,KAAG,IAAI,GAAG,QAAQ,KAAK;AACrC,cAAI,WAAS,OAAO,MAAI,CAAC;AACzB,aAAG,MAAM,IAAE,SAAS,GAAE;AAErB,gBAAI,SAAO,IAAI,WAAW;AAC1B,mBAAO,YAAU,WAAU;AAC1B,kBAAG,OAAO,GAAE;AACX,oBAAI,SAAO,aAAa,IAAI,WAAW,OAAO,MAAM,GAAE,QAAQ;AAC9D,oBAAG,CAAC,OAAO;AACX,oBAAG,UAAQ,IAAG;AACb,8BAAY;AACZ;AAAA,gBACD;AAAC;AAED,oBAAG,QAAO;AACT,+BAAa,MAAM;AAAE,2BAAO;AAAA,gBAC7B;AAAC;AACD,0BAAU,QAAQ,SAAS,MAAM;AAAA,cAClC,WAAS,CAAC,QAAO;AAChB,qBAAK,GAAG,iBAAgB,GAAE,gBAAgB,GAAE,CAAC;AAAA,cAC9C;AAAC;AAAA,YACF;AACA,mBAAO,kBAAkB,EAAE,IAAI;AAAA,UAChC;AACA,cAAG;AACF,eAAG,MAAM,CAAC,EAAE,aAAW,GAAG;AAC1B,iBAAK,GAAG,0CAAyC,GAAE,WAAU,SAAO,MAAI,oBAAkB,UAAS,cAAa,eAAe,CAAC;AAAA,UACjI,SAAO,GAAE;AACR,iBAAK,gBAAe,GAAE,CAAC;AACvB,wBAAY;AAAA,UACb;AAAC;AAAA,QACF;AAEC,iBAAS;AAAA,MACV;AACA,UAAI,YAAU,SAAS,QAAO;AAC7B,YAAG,OAAO,IAAK,QAAO,IAAI;AAC1B,YAAG,OAAO,IAAK,QAAO,IAAI;AAAA,MAC3B;AACA,UAAI,aAAW,SAAS,QAAO;AAC9B,eAAO,MAAI;AACX,YAAG,OAAO,IAAG;AACZ,iBAAO,GAAG,KAAK,YAAY,EAAC,MAAK,KAAI,CAAC;AACtC,iBAAO,GAAG,WAAW;AACrB,iBAAO,KAAG;AAAA,QACX;AAAC;AAAA,MACF;AACA,UAAI,aAAW,SAAS,QAAO;AAC9B,eAAO,MAAI;AACX,YAAG,OAAO,IAAG;AACZ,cAAG;AAAE,mBAAO,GAAG,KAAK;AAAA,UAAE,SAAO,GAAE;AAAE,iBAAK,eAAc,GAAE,CAAC;AAAA,UAAE;AACzD,iBAAO,KAAG;AAAA,QACX;AAAC;AAAA,MACF;AACA,UAAI,aAAW,SAAS,aAAY;AACnC,sBAAY,eAAa;AACzB,YAAI,WAAS,eAAa;AAE1B,YAAI,SAAO,YAAY;AACvB,YAAG,QAAO;AACT,cAAG,OAAO,IAAG;AACZ,mBAAO,GAAG,WAAW;AACrB,mBAAO,KAAG;AAAA,UACX;AAAC;AACD,cAAG,CAAC,OAAO,OAAO,OAAO,IAAG;AAC3B,qBAAS,YAAY,OAAO,EAAE;AAAA,UAC/B;AAAC;AACD,iBAAO,MAAI;AAAM,iBAAO,KAAG;AAC3B,cAAG,OAAO,IAAG;AACZ,mBAAO,OAAO,GAAG,MAAM;AACvB,mBAAO,KAAG;AAAA,UACX;AAAC;AACD,cAAG,OAAO,IAAG;AACZ,mBAAO,GAAG,WAAW;AACrB,mBAAO,GAAG,iBAAe,OAAO,KAAG;AAAA,UACpC;AAAC;AACD,qBAAW,MAAM;AACjB,qBAAW,MAAM;AAEjB,cAAG,UAAS;AACX,mBAAO,MAAM;AAAA,UACd;AAAC;AAAA,QACF;AAAC;AACD,oBAAY,SAAO;AAAA,MACpB;AAEA,UAAI,SAAO,SAAS,SAAO,SAAS,QAAO;AAC1C,YAAI,SAAO,QAAQ,MAAM;AACzB,iBAAQ,IAAE,GAAE,IAAE,OAAO,QAAO,KAAI;AAC/B,cAAI,QAAM,OAAO,CAAC;AAClB,gBAAM,QAAM,MAAM,KAAK;AAAA,QACxB;AAAC;AACD,eAAO,QAAM,OAAO,KAAK;AAAA,MAC1B;AAEA,UAAI,UAAQ,SAAS,QAAO;AAC3B,YAAI,OAAK,GAAE,OAAK,GAAE,MAAI,CAAC;AAEvB,YAAG,OAAO,gBAAe;AAAE,iBAAK,OAAO,eAAe;AAAG,iBAAK,OAAO,eAAe;AAAA,QAAG;AACvF,YAAG,CAAC,MAAK;AAAE,iBAAK,OAAO;AAAa,iBAAK,OAAO;AAAA,QAAa;AAC7D,iBAAQ,IAAE,GAAE,IAAE,OAAK,KAAK,SAAO,GAAE,IAAE,GAAE,IAAI,KAAI,KAAK,KAAK,CAAC,CAAC;AACzD,iBAAQ,IAAE,GAAE,IAAE,OAAK,KAAK,SAAO,GAAE,IAAE,GAAE,IAAI,KAAI,KAAK,KAAK,CAAC,CAAC;AACzD,eAAO;AAAA,MACR;AA4BA,eAAS,aAAW,SAAS,UAAS,eAAc,eAAc,eAAc,QAAO;AACtF,YAAI,MAAI;AACR,0BAAgB,gBAAc,CAAC;AAC/B,YAAI,QAAM,cAAc,SAAO;AAC/B,YAAI,SAAO,cAAc,UAAQ;AACjC,YAAI,YAAU,cAAc,aAAW;AAEvC,YAAI,SAAO,cAAc;AACzB,YAAG,UAAQ,OAAO,OAAK,OAAO,MAAI,OAAO,MAAI,iBAAiB,OAAO,OAAK,OAAO,OAAK,gBAAe;AACpG,mBAAO;AAAM,eAAK,GAAG,8BAA6B,GAAE,GAAG,GAAE,CAAC;AAAA,QAC3D;AAAC;AACD,YAAG,CAAC,QAAO;AACV,cAAG,iBAAe,eAAc;AAC/B,gBAAI,OAAK,gBAAc,gBAAc,IAAE,IAAE,IAAG,gBAAc,IAAG,IAAE;AAC/D,qBAAO,EAAC,IAAG,OAAK,SAAS,UAAU,MAAK,eAAc,IAAI,IAAE,EAAC;AAAA,UAC9D,OAAK;AACJ,gBAAI,OAAK,gBAAc,gBAAc,IAAE,IAAE,IAAG,gBAAc,IAAG,IAAE;AAC/D,qBAAO,EAAC,IAAG,OAAK,SAAS,UAAU,MAAK,eAAc,IAAI,IAAE,EAAC;AAAA,UAC9D;AAAC;AAAA,QACF;AAAC;AACD,eAAO,KAAG;AACV,eAAO,MAAI;AACX,YAAI,WAAS,OAAO;AAEpB,YAAI,YAAU,cAAc,aAAW,CAAC;AACxC,mBAAS,SAAO,CAAC;AACjB,YAAI,YAAU,OAAO,aAAW;AAChC,YAAG,OAAO,WAAU;AACnB,sBAAU,OAAO,aAAW,QAAM,OAAK;AAAA,QACxC;AAAC;AACD,YAAI,SAAO,OAAO,MAAM,OAAK;AAE7B,YAAI,OAAK,SAAS;AAClB,YAAG,QAAM,OAAK,GAAE;AACf,eAAK,GAAG,8BAA6B,GAAE,KAAI,QAAM,MAAI,IAAI,GAAE,CAAC;AAAA,QAC7D;AAAC;AACD,YAAI,OAAK;AACT,iBAAQ,IAAE,OAAM,IAAE,MAAK,KAAI;AAC1B,kBAAM,SAAS,CAAC,EAAE;AAAA,QACnB;AAAC;AAGD,YAAI,OAAK,gBAAc;AACvB,YAAG,OAAK,GAAE;AACT,iBAAK,KAAK,IAAI,GAAE,OAAK,KAAK,MAAM,MAAM,CAAC;AACvC,iBAAK,KAAK,MAAM,OAAK,IAAI;AAAA,QAC1B,WAAS,OAAK,GAAE;AACf,cAAI,YAAU,IAAE;AAChB,iBAAK,KAAK,MAAM,OAAK,SAAS;AAAA,QAC/B;AAAC;AAED,gBAAM,UAAU;AAChB,YAAI,MAAI,IAAI,WAAW,IAAI;AAC3B,YAAI,MAAI;AAER,iBAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAClC,cAAI,GAAG,IAAE,UAAU,CAAC;AACpB;AAAA,QACD;AAAC;AAED,eAAM,QAAM,MAAK,SAAS;AACzB,cAAI,IAAE,SAAS,KAAK,GAAG,QAAM,aAAa;AAC1C,cAAI,IAAE,QAAO,KAAG,EAAE;AAClB,cAAI,IAAE,YAAU,SAAS,OAAM,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG;AAEjD,cAAG,OAAK,GAAE;AACT,gBAAI,OAAK,MAAI,GAAG,OAAK;AACrB,qBAAQ,KAAG,GAAE,KAAG,IAAG,MAAK;AACvB,kBAAI,OAAK,EAAE,EAAE;AACb,kBAAG,OAAM;AACR,uBAAK,KAAK,IAAI,IAAG,KAAK,IAAI,GAAE,IAAI,CAAC;AACjC,uBAAK,OAAK,IAAE,OAAK,QAAO,OAAK;AAAA,cAC9B;AAEA,kBAAI,MAAI,KAAK,MAAM,IAAI;AACvB,sBAAM;AACN,kBAAI,MAAI,KAAK,MAAM,IAAI;AAGvB,kBAAI,KAAG,OAAK,SAAO,MAAI;AACvB,uBAAQ,IAAE,GAAE,MAAI,KAAI,OAAM,KAAI;AAC7B,oBAAI,IAAE,KAAK,MAAM,OAAM,IAAE,CAAE;AAC3B,oBAAG,GAAE;AACJ,uBAAG;AACH,uBAAG,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAClE,oBAAE,KAAK,EAAE;AAAI,oBAAE,KAAK;AAAI,oBAAE,KAAK,EAAE;AAAI,oBAAE,KAAK;AAC5C,sBAAE;AAAA,gBACH,OAAK;AAAE,sBAAE,WAAS,SAAS,CAAC,IAAE;AAAA,gBAAG;AAEjC,oBAAG,IAAE,MAAQ,KAAE;AAAA,yBAAgB,IAAE,OAAS,KAAE;AAC5C,oBAAG,OAAQ,SAAM,KAAK,IAAI,CAAC;AAC3B,oBAAI,GAAG,IAAE;AACT;AAAA,cACD;AAAC;AAED,qBAAK,YAAU;AACf,mBAAG;AAAA,YACJ;AACA,qBAAO,IAAE;AACT;AAAA,UACD;AAAC;AAED,mBAAQ,KAAG,GAAE,KAAG,GAAE,KAAG,IAAG,MAAK,MAAK;AACjC,gBAAG,KAAG,IAAG;AACR,kBAAI,OAAK,EAAE,EAAE;AACb,kBAAG,OAAM;AACR,uBAAK,KAAK,IAAI,IAAG,KAAK,IAAI,GAAE,IAAI,CAAC;AACjC,uBAAK,OAAK,IAAE,OAAK,QAAO,OAAK;AAAA,cAC9B;AACA,kBAAG,GAAE;AACJ,qBAAG;AACH,qBAAG,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAClE,kBAAE,KAAK,EAAE;AAAI,kBAAE,KAAK;AAAI,kBAAE,KAAK,EAAE;AAAI,kBAAE,KAAK;AAAA,cAC7C,OAAK;AAAE,qBAAG,WAAS,SAAS,IAAI,IAAE;AAAA,cAAM;AAAA,YACzC;AACA,iBAAG;AAAI,iBAAG;AACV,gBAAG,MAAI,GAAE;AAAE;AAAM;AAAA,YAAU;AAI3B,gBAAI,SAAS,KAAK,MAAM,CAAC;AACzB,gBAAG,MAAI,OAAO;AACd,gBAAI,QAAQ,KAAK,KAAK,CAAC;AACvB,gBAAI,UAAU,IAAI;AAElB,gBAAI,YAAU;AACd,gBAAI,WAAS,QAAM,KAAK,KAAK;AAC7B,gBAAI,MAAI,aAAW,WAAS,aAAW;AAEvC,gBAAG,MAAI,MAAQ,OAAI;AAAA,qBAAgB,MAAI,OAAS,OAAI;AACpD,gBAAG,OAAQ,SAAM,KAAK,IAAI,GAAG;AAC7B,gBAAI,GAAG,IAAE;AAET;AACA,iBAAG;AAAA,UACJ;AAAC;AACD,mBAAO,KAAK,IAAI,GAAG,IAAE,EAAE;AAAA,QACxB;AAAC;AACD,YAAG,OAAK,KAAK,MAAI,KAAG,MAAK;AACxB;AAAQ,gBAAI,IAAI,WAAW,IAAI,OAAO,MAAM,GAAG,OAAK,CAAC,CAAC;AAAA,QACvD;AAAC;AACD,YAAG,MAAI,KAAG,QAAQ,OAAK,KAAK,MAAK,MAAI,UAAQ,MAAI,cAAY,MAAK,CAAC;AAGnE,oBAAU;AACV,YAAI,gBAAc,OAAK;AACvB,YAAG,gBAAc,GAAE;AAClB,cAAI,SAAO,OAAK,iBAAe;AAC/B,sBAAU,IAAI,WAAW,IAAI,OAAO,MAAM,KAAK,CAAC;AAChD,gBAAI,IAAI,WAAW,IAAI,OAAO,MAAM,GAAE,KAAK,CAAC;AAAA,QAC7C;AAAC;AAED,YAAI,MAAI;AAAA,UACP;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UAEA;AAAA,UACA,YAAW;AAAA,UACX,MAAK;AAAA,QACP;AACA,YAAG,OAAQ,KAAI,OAAK;AACpB,eAAO;AAAA,MACR;AAQA,eAAS,YAAU,SAAS,YAAY,YAAY,MAAK;AACxD,YAAI,KAAK,IAAI,KAAK,KAAK,OAAO;AAC9B,YAAI,KAAK,KAAK,IAAI,EAAE;AACpB,YAAI,KAAK,KAAK,IAAI,EAAE;AACpB,YAAI,QAAQ,KAAK;AAEjB,YAAI,KAAK,IAAI;AACb,YAAI,KAAM,KAAK,KAAM;AACrB,YAAI,MAAM,IAAI,SAAS;AACvB,YAAG,YAAW;AACb,cAAI,MAAM,IAAI,MAAM,IAAI;AACxB,cAAI,MAAM,IAAI,MAAM;AAAA,QACrB,OAAK;AACJ,cAAI,MAAM,IAAI,MAAM,IAAI;AACxB,cAAI,KAAK,EAAE,IAAI,MAAM;AAAA,QACtB;AAEA,YAAI,KAAG,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,GAAE,KAAG;AAC1B,YAAI,KAAG,SAAS,GAAE;AACjB,cAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAChD,eAAK;AAAI,eAAK;AACd,eAAK;AAAI,eAAK;AACd,iBAAO;AAAA,QACR;AACA,WAAG,QAAM,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAM,IAAM,IAAM,GAAK;AACrD,eAAO;AAAA,MACR;AAQA,eAAS,aAAW,SAAS,WAAU,WAAU;AAShD,YAAI,QAAO,YAAU,aAAc;AACnC,YAAI;AACJ,YAAG,QAAM,MAAK;AACb,kBAAM,KAAK,MAAM,QAAM,OAAK,EAAE;AAAA,QAC/B,OAAK;AACJ,kBAAM,KAAK,MAAM,KAAK,IAAI,KAAI,KAAK,IAAI,IAAG,IAAE,KAAK,IAAI,QAAM,GAAK,IAAE,KAAK,IAAI,EAAE,KAAG,GAAG,CAAC,CAAC;AAAA,QACtF;AAAC;AACD,eAAO;AAAA,MACR;AAMA,eAAS,YAAU,SAAS,WAAU;AACrC,YAAI,MAAI,KAAK,IAAI,KAAK,aAAW,CAAC,GAAE,OAAK;AACzC,cAAI,KAAK,IAAI,KAAI,IAAI;AAGrB,cAAI,KAAG,KAAK,IAAI,MAAI,IAAI,IAAE,KAAK,IAAI,EAAE;AACrC,eAAO,KAAK,IAAI,MAAK,KAAK,MAAM,GAAG,CAAC;AAAA,MACrC;AAOA,eAAS,OAAK,SAAS,KAAI,KAAI;AAC9B,YAAG,OAAO,WAAS,SAAS;AAC5B,YAAI,MAAI,oBAAI,KAAK;AACjB,YAAI,KAAG,MAAI,IAAI,WAAW,GAAG,OAAO,EAAE,IACpC,OAAK,MAAI,IAAI,WAAW,GAAG,OAAO,EAAE,IACpC,OAAK,OAAK,IAAI,gBAAgB,GAAG,OAAO,EAAE;AAC5C,YAAI,QAAM,QAAM,KAAK,SAAO,KAAK,YAAU,KAAK;AAChD,YAAI,MAAI,CAAC,MAAI,IAAE,MAAI,UAAQ,QAAM,MAAI,QAAM,MAAI,MAAI,GAAG;AACtD,YAAI,IAAE,WAAU,MAAI,SAAS;AAC7B,YAAI,IAAE,GAAE,KAAG,IAAI,OAAK,QAAQ;AAC5B,YAAG,MAAM,GAAG,GAAE;AACb,eAAG,OAAK,IAAE,IAAI,SAAO,QAAQ,QAAM,OAAK,IAAE,IAAI,QAAM,QAAQ,OAAK;AAAA,QAClE,OAAK;AACJ,cAAE;AAAA,QACH;AAAC;AACD,eAAK,IAAE,EAAE,QAAO,KAAI;AACnB,cAAI,KAAK,EAAE,CAAC,CAAC;AAAA,QACd;AAAC;AACD,YAAG,SAAQ;AACV,gBAAI,GAAG,cAAY,IAAI,CAAC,GAAE,IAAI,SAAO,IAAE,MAAI,EAAE;AAAA,QAC9C,OAAK;AACJ,aAAG,MAAM,SAAQ,GAAG;AAAA,QACrB;AAAC;AAAA,MACF;AACA,UAAI,OAAK,WAAU;AAAE,iBAAS,KAAK,MAAM,MAAK,SAAS;AAAA,MAAG;AAC1D,UAAI,UAAQ;AAAK,UAAG;AAAC,kBAAQ,CAAC,QAAQ,IAAI;AAAA,MAAM,SAAO,GAAE;AAAA,MAAC;AAAC;AAK3D,UAAI,KAAG;AACP,eAAS,OAAO,KAAI;AACnB,YAAI,OAAK;AAAM,aAAK,KAAG,EAAE;AAGzB,gBAAQ;AAGR,YAAI,IAAE;AAAA,UACL,MAAK;AAAA,UAOJ,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAuBZ;AAEA,iBAAQ,KAAK,KAAI;AAChB,YAAE,CAAC,IAAE,IAAI,CAAC;AAAA,QACX;AAAC;AACD,aAAK,MAAI;AAET,YAAI,KAAG,EAAE,UAAU,GAAE,KAAG,EAAE,aAAa;AACvC,YAAG,MAAI,CAAC,MAAM,EAAE,KAAK,MAAI,CAAC,MAAM,EAAE,GAAE;AACnC,eAAK,KAAK,GAAG,EAAE,iBAAgB,CAAC,GAAG,sBAAqB,GAAE,eAAc,UAAU,CAAC,CAAC,GAAE,GAAE,GAAG;AAAA,QAC5F;AAAC;AACD,UAAE,UAAU,IAAE,CAAC,MAAI;AACnB,UAAE,aAAa,IAAE,CAAC,MAAI;AAEtB,aAAK,QAAM;AACX,aAAK,KAAG;AACR,aAAK,OAAK,EAAC,GAAE,GAAE,GAAE,EAAC;AAAA,MACnB;AAAC;AAED,eAAS,OAAK;AAAA;AAAA,QAAS,GAAE;AAAA;AAAA,QAAW,GAAE;AAAA,MAAC;AAEvC,eAAS,YAAU,OAAO,YAAU;AAAA,QACnC;AAAA,QAGC,cAAa,WAAU;AACvB,cAAG,KAAK,IAAI,cAAa;AACxB,mBAAO;AAAA,UACR,OAAK;AACJ,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,QAEC,YAAW,WAAU;AACrB,iBAAO,KAAK,aAAa,EAAE;AAAA,QAC5B;AAAA,QAEC,YAAW,WAAU;AACrB,cAAI,IAAE,KAAK,WAAW;AACtB,iBAAO,KAAG,EAAE;AAAA,QACb;AAAA,QAGC,MAAK,SAAS,MAAK,OAAM;AACzB,cAAI,OAAK,MAAK,MAAI,KAAK,KAAI,cAAY,KAAK,aAAa,GAAE,SAAO;AAClE,iBAAK,QAAM;AACX,cAAI,WAAS,SAAS,QAAO,gBAAe;AAC3C,6BAAe,CAAC,CAAC;AACjB,iBAAK,KAAK,GAAG,iBAAiB,IAAE,SAAO,qBAAmB,gBAAe,CAAC;AAC1E,gBAAG,OAAO,UAAS,YAAY,MAAM;AACrC,qBAAO,MAAM,QAAO,cAAc;AAAA,UACnC;AAEA,eAAK,aAAW;AAChB,cAAI,KAAG,WAAU;AAChB,iBAAK,KAAK,iBAAe,KAAK,KAAG,aAAW,KAAK,UAAU;AAC3D,iBAAK;AAEL,iBAAK,MAAI;AAAA,UACV;AAIA,cAAI,OAAK,YAAY;AACrB,cAAI,WAAS,EAAE,KAAK,GAAE,YAAU,KAAK;AACrC,eAAK,KAAG,KAAK,MAAI;AACjB,eAAK,MAAI,KAAK;AACd,cAAI,WAAS,WAAU;AAEtB,gBAAG,aAAW,KAAK,KAAK,CAAC,KAAK,IAAG;AAChC,kBAAI,MAAI,GAAG,eAAe;AAC1B,kBAAG,YAAU,KAAK,GAAE;AAEnB,qBAAK,MAAM;AAAA,cACZ,OAAK;AACJ,sBAAI,GAAG,eAAe;AAAA,cACvB;AAAC;AACD,uBAAS,GAAG;AACZ,qBAAO;AAAA,YACR;AAAC;AAAA,UACF;AAGA,cAAG,CAAC,WAAU;AACb,qBAAS,GAAG,EAAE,gBAAe,CAAC,MAAM,CAAC,IAAE,GAAG,2BAA2B,IAAE,MAAI,SAAO,kCAAkC;AACpH;AAAA,UACD;AAAC;AACD,cAAI,WAAS,KAAK,SAAS,EAAC,SAAQ,MAAK,YAAW,KAAI,CAAC;AACzD,cAAG,UAAS;AACX,qBAAS,GAAG,aAAa,IAAE,QAAQ;AACnC;AAAA,UACD;AAAC;AAGD,cAAI;AACJ,cAAI,SAAO,WAAU;AACpB,kBAAI,IAAI;AACR,gBAAG,CAAC,IAAI,OAAI,SAAO,SAAS,WAAW,IAAI;AAAA,UAC5C;AAGA,cAAG,IAAI,cAAa;AACnB,iBAAK,aAAW;AAChB,gBAAG,CAAC,SAAS,WAAW,GAAE;AACzB,uBAAS,GAAG,sBAAsB,CAAC;AACnC;AAAA,YACD;AAAC;AACD,mBAAO;AAEP,uBAAW,WAAW;AACtB,gBAAI,SAAO,KAAK,SAAO,IAAI;AAC3B,mBAAO,KAAG;AACV,mBAAO,MAAI,IAAI;AACf,mBAAO,QAAM,CAAC;AAEd,gBAAG;AACF,sBAAQ,WAAW;AAAA,YACpB,SAAO,GAAE;AACR,yBAAW,WAAW;AACtB,uBAAS,GAAG,kBAAkB,IAAE,EAAE,OAAO;AACzC;AAAA,YACD;AACA,eAAG;AACH;AAAA,UACD;AAAC;AAID,cAAI,WAAS,SAAS,MAAK,KAAI;AAC9B,gBAAG;AACF,qBAAO,IAAI;AAAA,YACZ,SAAO,GAAE;AACR,uBAAS,GAAG,2CAA0C,GAAE,2BAA2B,CAAC;AACpF;AAAA,YACD;AAAC;AAED,gBAAG,SAAS,GAAE,IAAI,GAAE;AACnB,kBAAG,SAAS,KAAK,IAAI,GAAE;AACtB,yBAAS,MAAI,GAAG,eAAe,CAAC;AAAA,cACjC,OAAK;AACJ,yBAAS,GAAG;AAAA,cACb;AAAC;AAAA,YACF;AAAC;AAAA,UACF;AACA,cAAI,WAAS,SAAS,MAAK,MAAK;AAC/B,gBAAG,oBAAoB,KAAK,IAAI,GAAE;AACjC,kBAAG,KAAM,UAAS,GAAG,iBAAiB,GAAE,IAAI;AAAA,YAC7C,WAAS,OAAO,oBAAkB,OAAM;AACvC,kBAAG,KAAM,UAAS,GAAG,+BAA+B,CAAC;AAAA,YACtD,OAAK;AACJ,qBAAO;AAAA,YACR;AAAC;AAAA,UACF;AAIA,cAAG,SAAS,OAAO,GAAE;AACpB,eAAG;AACH;AAAA,UACD;AAAC;AACD,cAAG,CAAC,SAAS,QAAQ,GAAE;AACtB,qBAAS,IAAG,GAAG,iBAAiB,CAAC;AACjC;AAAA,UACD;AAAC;AACD,iBAAO;AAGP,cAAI,KAAG,SAASC,SAAO;AAEtB,uBAAW,WAAU;AACpB,cAAAA,QAAO,QAAM,CAAC;AACd,kBAAI,YAAU,SAAS;AACvB,kBAAG,WAAU;AACZ,2BAAW;AACX,gBAAAA,QAAO,QAAM,UAAU;AAAA,cACxB;AAAC;AACD,uBAAS,SAAOA;AAChB,cAAAA,QAAO,KAAG;AACV,cAAAA,QAAO,MAAI,IAAI;AACf,kBAAG,SAAS,EAAE;AAEd,kBAAG,SAAS,OAAO,GAAE;AACpB,oBAAG,UAAU,MAAK,KAAK,GAAG,oBAAoB,GAAE,CAAC;AAEjD,wBAAQ,WAAW;AACnB,mBAAG;AAAA,cACJ,OAAK;AACJ,yBAAS,GAAG,mBAAmB,CAAC;AAAA,cACjC;AAAC;AAAA,YACF,GAAE,GAAG;AAAA,UACN;AACA,cAAI,KAAG,SAAS,GAAE;AACjB,gBAAI,OAAK,EAAE,QAAM,EAAE,WAAS,EAAE,OAAK,MAAI;AACvC,gBAAI,SAAO;AACX,gBAAG,eAAa,KAAK,SAAS,GAAE,IAAI,GAAE;AACrC,uBAAO,GAAG,qBAAqB;AAAA,YAChC;AACA,gBAAI,OAAK,GAAG,gBAAgB,GAAE,OAAK,GAAG,aAAa;AACnD,iBAAK,KAAK,OAAK,SAAO,MAAI,GAAG,UAAQ,OAAM,IAAE,GAAE,CAAC;AAEhD,gBAAG,QAAO;AACT,qBAAK;AAAM,qBAAK;AAChB,4BAAc,CAAC;AAAA,YAChB,WAAS,MAAK;AACb,mBAAK,KAAK,OAAK,MAAI,MAAK,GAAE,IAAI;AAC9B,uBAAS,MAAK,OAAK,IAAI;AAAA,YACxB,OAAK;AACJ,uBAAS,MAAK,OAAK,CAAC;AAAA,YACrB;AAAC;AAAA,UACF;AAEA,cAAI,cAAY,GAAE,MAAK;AACvB,cAAI,gBAAc,SAAS,OAAM;AAChC;AACA,gBAAI,SAAO;AACX,gBAAI,QAAM,mBAAkB,QAAM,oBAAmB,QAAM;AAC3D,gBAAI,WAAS,SAAO,OAAK,QAAM,MAAI,QAAM,MAAI,QAAM;AACnD,gBAAI,WAAS,KAAK,MAAM,OAAO,IAAI,MAAM,KAAG,IAAI,CAAC;AACjD,iBAAK,KAAK,aAAW,cAAY,MAAI,SAAO,MAAI,OAAO,QAAQ,CAAC;AAEhE,gBAAG,OAAM;AACR,kBAAG,OAAO,YAAW,SAAS,YAAS,CAAC;AACxC,uBAAS,KAAK,IAAE;AAChB,uBAAS,KAAK,IAAE;AAChB,uBAAS,KAAK,IAAE;AAAA,YACjB;AAAC;AAGD,gBAAG,SAAS,aAAa,GAAE;AAC1B,mBAAK,KAAK,GAAG,sDAAqD,GAAE,SAAO,MAAI,aAAa,GAAE,CAAC;AAAA,YAChG;AAAC;AAED,gBAAI,OAAK,EAAC,OAAM,UAAU,OAAM,IAAI,iBAAe,MAAK;AACxD,gBAAG;AACF,kBAAI,MAAI,SAAS,MAAM,eAAe,EAAE,MAAK,IAAG,EAAE;AAAA,YACnD,SAAO,GAAE;AACR,mBAAK,KAAK,iBAAgB,GAAE,CAAC;AAC7B,qBAAK,EAAC,OAAM,MAAM,OAAM,MAAK;AAC7B,oBAAI,SAAS,MAAM,eAAe,EAAE,MAAK,IAAG,EAAE;AAAA,YAC/C;AAAC;AACD,iBAAK,KAAK,kBAAgB,MAAI,OAAO,IAAI,IAAE,OAAK,SAAS,GAAG,IAC1D,GAAG,0HAAyH,GAAE,UAAS,MAAM,IAC7I,MAAI,SAAO,UAAQ,KAAG,SAAO,UAAU,SAAS;AAClD,gBAAG,OAAK,IAAI,MAAK;AAChB,kBAAI,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE;AAAA,YAC1B;AAAC;AAAA,UACF;AACA,wBAAc;AAAA,QACf;AAAA,QAEC,OAAM,SAAS,MAAK;AACpB,iBAAK,QAAM;AAEX,cAAI,OAAK,MAAK,cAAY,KAAK,aAAa;AAC5C,eAAK,MAAM;AACX,cAAI,OAAK,aAAW,KAAK;AAEzB,cAAI,OAAK,YAAY;AACrB,eAAK,KAAG;AACR,cAAG,KAAK,OAAK,KAAK,GAAE;AAEnB,iBAAK,KAAK,GAAG,+CAA+C,IAAE,MAAK,CAAC;AACpE,iBAAK;AACL;AAAA,UACD;AAAC;AACD,eAAK;AAEL,qBAAW,WAAW;AAEtB,eAAK,KAAK,WAAS,IAAI;AACvB,eAAK;AAAA,QACN;AAAA,QAOC,MAAK,SAAS,SAAQ,eAAc;AACpC,cAAI,OAAK;AACT,eAAK,MAAM;AAEX,eAAK,SAAO;AACZ,eAAK,cAAY;AACjB,eAAK,UAAQ,CAAC,OAAO;AACrB,eAAK,UAAQ,QAAQ;AACrB,eAAK,UAAU,aAAa;AAC5B,eAAK,aAAW;AAChB,iBAAO;AAAA,QACR;AAAA,QACC,WAAU,SAAS,YAAW;AAC9B,cAAI,OAAK,MAAK,MAAI,KAAK;AACvB,cAAI,QAAM,IAAI,aAAa;AAC3B,cAAG,QAAM,YAAW;AACnB,gBAAI,aAAa,IAAE;AAAA,UACpB,OAAK;AAAE,oBAAM;AAAA,UAAE;AACf,eAAK,gBAAgB,IAAE;AACvB,eAAK,KAAK,mBAAiB,OAAK,aAAW,UAAQ,gBAAc,OAAK,IAAI,aAAa,KAAG,QAAM,MAAI,GAAG,UAAU,IAAE,OAAK,QAAM,KAAK,QAAM,IAAE,CAAC;AAAA,QAC7I;AAAA,QACC,UAAS,SAAS,SAAQ;AAE1B,cAAI,QAAO,OAAK,MAAK,MAAI,KAAK;AAG9B,cAAI,MAAI;AACR,cAAG,CAAC,UAAU,CAAC,SAAS,GAAG,KAAK,OAAO,aAAW,cAAc,CAAC,IAAI,UAAU,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,GAAE;AAC7G,oBAAQ,GAAG;AACX,qBAAO,GAAG,kBAAiB,GAAE,GAAG;AAAA,UACjC;AAAC;AAGD,cAAG,CAAC,QAAO;AACV,gBAAI,OAAK,IAAI,MAAK,QAAM,KAAK,OAAK,WAAW;AAC7C,gBAAG,IAAI,oBAAmB;AACzB,kBAAG,CAAC,OAAM;AACT,yBAAO,GAAG,sCAAqC,GAAE,IAAI,KAAG,KAAK,IAAI,IAAE,KAAG,GAAG,gBAAgB;AAAA,cAC1F,WAAS,CAAC,QAAQ,YAAW;AAC5B,yBAAO,GAAG,sBAAqB,GAAE,QAAQ,OAAO;AAAA,cACjD;AAAC;AAAA,YACF;AAAC;AAED,gBAAG,CAAC,UAAU,OAAM;AACnB,uBAAO,KAAK,OAAK,WAAW,EAAE,SAAQ,GAAG;AAAA,YAC1C;AAAC;AAAA,UACF;AAAC;AAED,iBAAO,UAAQ;AAAA,QAChB;AAAA,QACC,UAAS,SAAS,aAAY,YAAW;AACzC,cAAI,OAAK,MAAK,MAAI,KAAK;AACvB,eAAK,SAAO,cAAY,IAAE;AAC1B,eAAK,cAAY;AACjB,eAAK,UAAQ,CAAC;AACd,eAAK,UAAQ;AACb,cAAG,aAAY;AACd,iBAAK,aAAW,SAAO,YAAY;AAAA,UACpC;AAAC;AAED,eAAK,QAAM;AACX,eAAK,YAAU;AACf,eAAK,aAAW;AAChB,eAAK,WAAS;AACd,eAAK,aAAW,CAAC;AAGjB,eAAK,UAAU,UAAU;AAEzB,eAAK,YAAU;AAEf,cAAG,KAAK,IAAI,OAAK,QAAQ,GAAE;AAC1B,gBAAI,YAAU,KAAK,YAAU,KAAK,IAAI,OAAK,QAAQ,EAAE,GAAG;AACxD,gBAAG,WAAU;AACZ,wBAAU,WAAS,CAAC;AACpB,wBAAU,UAAQ;AAAA,YACnB;AAAC;AAAA,UACF;AAAC;AAAA,QACF;AAAA,QACC,WAAU,WAAU;AAEpB,eAAK,aAAW,CAAC;AAAA,QAClB;AAAA,QACC,OAAM,SAAS,KAAI,KAAI;AACvB,cAAI,OAAK,MAAK,MAAI,KAAK,KAAI,YAAU,KAAK;AAC1C,cAAG,KAAK,SAAO,GAAE;AAChB,gBAAG,CAAC,KAAK,MAAM,MAAK,KAAK,oBAAmB,CAAC;AAC7C;AAAA,UACD;AAAC;AACD,cAAI,mBAAiB,KAAK,gBAAgB;AAC1C,cAAI,OAAK,IAAI;AACb,cAAI,aAAW,SAAS,WAAW,KAAI,IAAI;AAE3C,cAAI,UAAQ,KAAK;AACjB,cAAI,iBAAe,QAAQ;AAC3B,kBAAQ,KAAK,GAAG;AAGhB,cAAI,cAAY;AAChB,cAAI,qBAAmB;AAGvB,cAAI,MAAI,KAAK,IAAI;AACjB,cAAI,UAAQ,KAAK,MAAM,OAAK,mBAAiB,GAAI;AACjD,eAAK,YAAU;AACf,cAAG,KAAK,QAAQ,UAAQ,GAAE;AACzB,iBAAK,aAAW,MAAI;AAAA,UACrB;AAAC;AACD,cAAI,aAAW,KAAK;AACpB,qBAAW,OAAO,GAAE,GAAE,EAAC,GAAE,KAAI,GAAE,QAAO,CAAC;AAEvC,cAAI,YAAU,KAAI,QAAM;AACxB,mBAAQ,IAAE,GAAE,IAAE,WAAW,QAAO,KAAI;AACnC,gBAAI,IAAE,WAAW,CAAC;AAClB,gBAAG,MAAI,EAAE,IAAE,KAAK;AACf,yBAAW,SAAO;AAClB;AAAA,YACD;AAAC;AACD,wBAAU,EAAE;AACZ,qBAAO,EAAE;AAAA,UACV;AAAC;AAED,cAAI,WAAS,WAAW,CAAC;AACzB,cAAI,OAAK,MAAI;AACb,cAAI,OAAK,OAAK;AACd,cAAI,OAAK,OAAK,MAAM,YAAU,OAAK,OAAQ,WAAW,UAAQ,IAAI;AAEjE,gBAAI,UAAQ,MAAI,SAAS,IAAE;AAC3B,gBAAG,UAAQ,UAAQ,GAAE;AACpB,kBAAI,UAAQ,CAAC,IAAI;AACjB,mBAAK,KAAK,MAAI,MAAI,MAAI,KAAK,IAAI,UAAQ,GAAG,iBAAgB,CAAC,IAAE,GAAG,kBAAiB,CAAC,GAAE,CAAC,OAAO,CAAC,GAAE,CAAC;AAChG,mBAAK,YAAU;AAGf,kBAAG,SAAQ;AACV,oBAAI,SAAO,IAAI,WAAW,UAAQ,mBAAiB,GAAI;AACvD,wBAAM,OAAO;AACb,wBAAQ,KAAK,MAAM;AAAA,cACpB;AAAC;AAAA,YACF;AAAC;AAAA,UACF;AAAC;AAGD,cAAI,UAAQ,KAAK,SAAQ,UAAQ;AACjC,cAAI,aAAW,UAAQ;AACvB,eAAK,UAAQ;AAIb,cAAG,WAAU;AAEZ,gBAAI,YAAU,SAAS,WAAW,SAAQ,kBAAiB,IAAI,aAAa,GAAE,UAAU,SAAS;AACjG,sBAAU,YAAU;AAEpB,sBAAQ,UAAU;AAClB,sBAAQ,UAAU,KAAK;AACvB,yBAAW,UAAQ;AACnB,sBAAU,UAAQ;AAElB,sBAAQ,UAAU;AAClB,6BAAe,QAAQ;AACvB,oBAAQ,KAAK,UAAU,IAAI;AAC3B,+BAAiB,UAAU,aAAa;AAAA,UACzC;AAAC;AAED,cAAI,WAAS,KAAK,MAAM,aAAW,mBAAiB,GAAI;AACxD,cAAI,gBAAc,QAAQ;AAC1B,cAAI,oBAAkB,YAAY;AAGlC,cAAI,WAAS,WAAU;AAEtB,gBAAI,MAAI,aAAW,IAAE,CAAC;AACtB,gBAAIC,YAAS,QAAQ,CAAC,KAAG;AACzB,qBAAQC,KAAE,gBAAeA,KAAE,eAAcA,MAAI;AAC5C,kBAAI,SAAO,QAAQA,EAAC;AACpB,kBAAG,UAAQ,MAAK;AACf,gBAAAD,YAAS;AAAA,cACV,OAAK;AACJ,uBAAK,OAAO;AAGZ,oBAAG,aAAW,OAAO,QAAO;AAC3B,uBAAK,IAAI,OAAK,SAAS,EAAE,WAAU,MAAM;AAAA,gBAC1C;AAAC;AAAA,cACF;AAAC;AAAA,YACF;AAAC;AAGD,gBAAGA,aAAY,WAAU;AACxB,kBAAIC,KAAE;AACN,kBAAG,YAAY,CAAC,GAAE;AACjB,gBAAAA,KAAE;AAAA,cACH;AAAC;AACD,qBAAKA,KAAE,mBAAkBA,MAAI;AAC5B,4BAAYA,EAAC,IAAE;AAAA,cAChB;AAAC;AAAA,YACF;AAAC;AAGD,gBAAGD,WAAS;AACX,oBAAI,aAAW,UAAQ;AAEvB,sBAAQ,CAAC,IAAE;AAAA,YACZ;AAAC;AACD,gBAAG,WAAU;AACZ,wBAAU,WAAS;AAAA,YACpB,OAAK;AACJ,mBAAK,WAAS;AAAA,YACf;AAAC;AAAA,UACF;AAEA,cAAI,aAAW,GAAE,UAAQ;AACzB,cAAG;AACF,yBAAW,IAAI,UAAU,SAAQ,YAAW,UAAS,kBAAiB,gBAAe,QAAQ;AAC7F,yBAAW,eAAa;AAAA,UACzB,SAAO,GAAE;AAER,oBAAQ,MAAM,UAAQ,GAAG,0BAA0B,GAAE,CAAC;AAAA,UACvD;AAAC;AAED,cAAI,QAAM,KAAK,IAAI,IAAE;AACrB,cAAG,QAAM,MAAM,KAAK,aAAW,MAAI,KAAK;AACvC,iBAAK,KAAK,UAAQ,GAAG,qBAAoB,GAAE,KAAK,GAAE,CAAC;AAAA,UACpD;AAAC;AAED,cAAG,YAAW;AAEb,gBAAI,WAAS;AACb,qBAAQ,IAAE,gBAAe,IAAE,eAAc,KAAI;AAC5C,kBAAG,QAAQ,CAAC,KAAG,MAAK;AACnB,2BAAS;AAAA,cACV,OAAK;AACJ,wBAAQ,CAAC,IAAE,IAAI,WAAW,CAAC;AAAA,cAC5B;AAAC;AAAA,YACF;AAAC;AAED,gBAAG,UAAS;AACX,mBAAK,KAAK,GAAG,yBAAyB,GAAE,CAAC;AAAA,YAC1C,OAAK;AAEJ,kBAAG,WAAU;AACZ,0BAAU,WAAS;AAAA,cACpB,OAAK;AACJ,qBAAK,WAAS;AAAA,cACf;AAAC;AAAA,YACF;AAAC;AAAA,UACF,OAAK;AACJ,qBAAS;AAAA,UACV;AAAC;AAAA,QACF;AAAA,QAMC,OAAM,WAAU;AAChB,cAAI,OAAK;AAET,cAAI,SAAO;AACX,cAAG,KAAK,IAAI,cAAa;AACxB,gBAAG,CAAC,KAAK,QAAO;AACf,uBAAO;AAAA,YACR;AAAA,UACD,WAAS,CAAC,SAAS,OAAO,GAAE;AAC3B,qBAAO;AAAA,UACR;AAAC;AACD,cAAG,CAAC,QAAO;AACV,iBAAK,KAAK,GAAG,qBAAqB,GAAE,CAAC;AACrC;AAAA,UACD;AAAC;AACD,cAAI,MAAI,KAAK,WAAW;AACxB,eAAK,KAAK,GAAG,mBAAmB,IAAE,SAAS,GAAG,IAAE,aAAW,KAAK,UAAU;AAE1E,eAAK,MAAM;AACX,eAAK,SAAS,MAAM,IAAI,aAAa,CAAC;AACtC,eAAK,QAAM;AAGX,cAAG,KAAK,OAAK,KAAK,MAAI,KAAG,KAAK,IAAG;AAEhC,iBAAK,KAAK,GAAG,gBAAgB,GAAE,CAAC;AAChC;AAAA,UACD;AAAC;AACD,eAAK,MAAI;AAET,cAAI,MAAI,WAAU;AACjB,gBAAG,KAAK,SAAO,GAAE;AAChB,mBAAK,QAAM;AACX,mBAAK,OAAO;AAAA,YACb;AAAA,UACD;AACA,cAAI,MAAI;AAGR,cAAI,SAAO,KAAK,WAAW;AAC3B,iBAAO,MAAM,KAAK,EAAE,IAAE,WAAU;AAC/B,iBAAK,KAAK,MAAI,IAAI,QAAM,YAAY;AACpC,gBAAI;AAAA,UACL;AACA,oBAAU,KAAI,SAAS,MAAK;AAC3B,oBAAM,KAAK,KAAK,MAAI,SAAS;AAC7B,mBAAO,KAAK,SAAO;AAAA,UACpB,GAAE,SAAS,MAAK;AACf,oBAAM,KAAK,KAAK,MAAI,IAAI,KAAK;AAC7B,gBAAI;AAAA,UACL,GAAE,SAAS,KAAI;AACd,iBAAK,KAAK,MAAI,IAAI,QAAM,GAAG,gBAAgB,IAAE,KAAI,CAAC;AAClD,gBAAI;AAAA,UACL,CAAC;AAAA,QACF;AAAA,QAKC,OAAM,WAAU;AAChB,cAAI,OAAK,MAAK,SAAO,KAAK,WAAW;AACrC,cAAG,KAAK,OAAM;AACb,iBAAK,QAAM;AACX,iBAAK,KAAK,OAAO;AACjB,gBAAG,OAAQ,QAAO,OAAO,MAAM,KAAK,EAAE;AAAA,UACvC;AAAC;AAAA,QACF;AAAA,QAEC,QAAO,WAAU;AACjB,cAAI,OAAK,MAAK,SAAO,KAAK,WAAW;AACrC,cAAI,MAAI,UAAS,OAAK,MAAI;AAC1B,cAAG,KAAK,SAAO,GAAE;AAChB,iBAAK,KAAK,IAAI;AAAA,UACf,WAAS,KAAK,OAAM;AACnB,iBAAK,QAAM;AACX,iBAAK,KAAK,GAAG;AACb,iBAAK,UAAU;AAEf,gBAAG,QAAO;AACT,qBAAO,MAAM,KAAK,EAAE,IAAE,SAAS,KAAI,KAAI;AACtC,oBAAG,KAAK,SAAO,GAAE;AAChB,uBAAK,MAAM,KAAI,GAAG;AAAA,gBACnB;AAAC;AAAA,cACF;AACA,wBAAU,MAAM;AAAA,YACjB;AAAC;AAED,gBAAI,MAAI,KAAK,WAAW;AACxB,gBAAG,KAAI;AACN,wBAAU,KAAI,SAAS,MAAK;AAC3B,wBAAM,KAAK,KAAK,OAAK,KAAK;AAC1B,uBAAO,KAAK,SAAO;AAAA,cACpB,GAAE,SAAS,MAAK;AACf,wBAAM,KAAK,KAAK,OAAK,IAAI,KAAK;AAC9B,0BAAU,MAAM;AAAA,cACjB,GAAE,SAAS,KAAI;AACd,qBAAK,KAAK,OAAK,IAAI,QAAM,UAAQ,KAAI,CAAC;AAAA,cACvC,CAAC;AAAA,YACF;AAAC;AAAA,UACF;AAAC;AAAA,QACF;AAAA,QAKC,OAAM,SAAS,YAAW;AAC1B,cAAI,OAAK,MAAK,MAAI,KAAK;AACvB,cAAG,CAAC,KAAK,QAAO;AACf,iBAAK;AAAA,UACN;AAAC;AACD,cAAG,KAAK,OAAM;AACb,iBAAK,MAAM;AACX,iBAAK,QAAM;AAAA,UACZ;AAAC;AACD,cAAG,CAAC,cAAc,KAAK,IAAI,OAAK,OAAO,GAAE;AACxC,iBAAK,IAAI,OAAK,OAAO,EAAE,KAAK,SAAS;AACrC,iBAAK,YAAU;AAAA,UAChB;AAAC;AAAA,QACF;AAAA,QAUC,MAAK,SAAS,MAAK,OAAM,WAAU;AACnC,cAAI,OAAK,MAAK,MAAI,KAAK,KAAI;AAC3B,cAAI,UAAQ,KAAK,YAAU,KAAK,YAAY,WAAS,WAAS,KAAK,QAAQ;AAC3E,eAAK,KAAK,GAAG,sBAAsB,KAChC,UAAQ,UAAQ,QAAM,GAAG,WAAW,IAAE,KAAK,WAAS,cAC1C,WAAS,WAAS,WAAS,UAAQ,KAAM,QAAQ,CAAC,IAC7D,OAAK,aAAW,KAAK,aAAW,OAAK,SAAO,UAAQ,EAAE;AAExD,cAAI,MAAI,WAAU;AACjB,iBAAK,MAAM;AACX,gBAAG,WAAU;AACZ,mBAAK,MAAM;AAAA,YACZ;AAAC;AAAA,UACF;AACA,cAAI,MAAI,SAAS,KAAI;AACpB,iBAAK,KAAK,GAAG,eAAe,IAAE,KAAI,CAAC;AACnC,qBAAO,MAAM,GAAG;AAChB,gBAAI;AAAA,UACL;AACA,cAAI,KAAG,SAAS,MAAK,MAAKE,WAAS;AAClC,gBAAI,QAAM,QAAO,QAAM,eAAc,MAAI,YAAW,OAAK;AACzD,gBAAI,QAAM,KAAK,GAAG,KAAG,SAAS,IAAI,KAAG,OAAM,OAAK,MAAI,MAAI;AACxD,gBAAI,OAAK,gBAAgB,aAAY,OAAK;AAC1C,gBAAI,OAAK,OAAK,KAAK,aAAW,KAAK;AACnC,gBAAG,SAAO,OAAM;AACf,kBAAG,CAAC,KAAM,QAAK;AAAA,YAChB,WAAS,SAAO,OAAM;AACrB,kBAAG,OAAO,QAAM,YAAW;AAC1B,uBAAK,GAAG,EAAE,gBAAe,CAAC,IAAI,CAAC,IAAE,GAAG,iBAAgB,GAAE,SAAO,MAAI,OAAK,OAAK,QAAM,GAAG;AAAA,cACrF,OAAK;AACJ,oBAAG,KAAM,QAAK,IAAI,KAAK,CAAC,IAAI,GAAE,EAAC,MAAK,KAAI,CAAC;AACzC,oBAAG,EAAE,gBAAgB,MAAO,QAAK;AACjC,uBAAK,KAAK,QAAM;AAAA,cACjB;AAAA,YACD,OAAK;AACJ,qBAAK,GAAG,EAAE,gBAAe,CAAC,IAAI,CAAC;AAAA,YAChC;AAAC;AAED,iBAAK,KAAK,GAAG,0CAAyC,GAAE,KAAK,IAAI,IAAE,IAAGA,WAAS,IAAI,IAAE,MAAI,OAAK,MAAI,IAAI;AACtG,gBAAG,MAAK;AACP,kBAAI,QAAM,IAAE,OAAK,GAAG,wBAAuB,GAAE,IAAI,MAAK,KAAK,IAAE,OAAK,IAAI;AACtE;AAAA,YACD;AAAC;AACD,gBAAG,IAAI,oBAAmB;AACzB,mBAAK,KAAK,GAAG,mDAAmD,GAAE,CAAC;AAAA,YACpE,WAAS,OAAK,KAAK,IAAI,IAAGA,YAAS,CAAC,GAAE;AACrC,kBAAI,GAAG,kBAAiB,GAAE,IAAI,IAAI,CAAC;AACnC;AAAA,YACD;AAAC;AAED,oBAAM,KAAK,MAAKA,WAAS,IAAI;AAC7B,gBAAI;AAAA,UACL;AACA,cAAG,CAAC,KAAK,QAAO;AACf,gBAAI,YAAU,KAAK,SAAO;AAC1B,gBAAG,CAAC,KAAK,SAAS,WAAU;AAC3B,kBAAI,GAAG,aAAa,KAAG,YAAU,GAAG,oCAAoC,IAAE,GAAG;AAC7E;AAAA,YACD;AAAC;AAAA,UACF;AAAC;AACD,eAAK,MAAM,IAAI;AACf,cAAI,OAAK,KAAK;AACd,cAAG,CAAC,MAAK;AACR,gBAAI,GAAG,cAAc,CAAC;AACtB;AAAA,UACD;AAAC;AACD,cAAG,CAAC,KAAK,IAAI,IAAI,GAAE;AAClB,gBAAI,GAAG,mDAAkD,GAAE,IAAI,MAAK,MAAM,CAAC;AAC3E;AAAA,UACD;AAAC;AAGD,cAAG,KAAK,QAAO;AACd,gBAAI,WAAS,KAAK,SAAS,KAAK,eAAa,EAAC,SAAQ,QAAO,YAAW,MAAK,CAAC;AAC9E,gBAAG,UAAS;AACX,kBAAI,GAAG,aAAa,IAAE,QAAQ;AAC9B;AAAA,YACD;AAAC;AAAA,UACF;AAAC;AAGD,cAAI,YAAU,KAAK;AACnB,cAAG,KAAK,IAAI,OAAK,WAAW,KAAG,WAAU;AACxC,gBAAI,WAAS,KAAK,MAAM,UAAU,UAAQ,IAAI,aAAa,IAAE,GAAI;AAEjE,iBAAG,KAAK,IAAI;AACZ,iBAAK,IAAI,OAAK,WAAW,EAAE,WAAU,SAAS,MAAK,MAAK;AACvD,iBAAG,MAAK,MAAK,QAAQ;AAAA,YACtB,GAAE,GAAG;AACL;AAAA,UACD;AAAC;AAGD,eAAG,KAAK,IAAI;AACZ,cAAG,CAAC,KAAK,QAAQ,CAAC,GAAE;AACnB,gBAAI,GAAG,oBAAoB,CAAC;AAC5B;AAAA,UACD;AAAC;AACD,cAAI,QAAM,SAAS,WAAW,KAAK,SAAQ,KAAK,gBAAgB,GAAE,IAAI,aAAa,CAAC;AAEpF,cAAI,aAAa,IAAE,MAAM,aAAa;AACtC,cAAI,MAAI,MAAM;AACd,cAAI,WAAS,KAAK,MAAM,IAAI,SAAO,IAAI,aAAa,IAAE,GAAI;AAE1D,eAAK,KAAK,GAAG,wBAAuB,GAAE,OAAK,OAAK,IAAI,QAAO,KAAK,IAAI,IAAE,EAAE,CAAC;AAEzE,qBAAW,WAAU;AACpB,iBAAG,KAAK,IAAI;AACZ,iBAAK,IAAI,IAAI,EAAE,KAAI,SAAS,MAAK,MAAK;AACrC,iBAAG,MAAK,MAAK,QAAQ;AAAA,YACtB,GAAE,SAAS,KAAI;AACd,kBAAI,GAAG;AAAA,YACR,CAAC;AAAA,UACF,CAAC;AAAA,QACF;AAAA,MAED;AAOA,UAAI,eAAa,SAAS,SAAS,OAAM;AACxC,YAAG,CAAC,MAAM,KAAI;AACb,gBAAM,MAAI,CAAC,CAAC;AAAG,gBAAM,SAAO,CAAC;AAAG,gBAAM,QAAM,CAAC;AAAA,QAC9C;AAAC;AACD,YAAI,SAAO,MAAM,QAAQ,WAAS,CAAC,MAAM,IAAI,CAAC,CAAC;AAC/C,YAAI,SAAO,WAAU;AAAE,gBAAM,IAAI,CAAC,IAAE,SAAS,CAAC;AAAA,QAAE;AAEhD,YAAI,MAAI,MAAM,MAAM;AACpB,YAAI,QAAM,IAAI,WAAW,MAAI,QAAQ,MAAM;AAC3C,cAAM,IAAI,MAAM,KAAK;AAAG,cAAM,IAAI,SAAQ,GAAG;AAC7C,cAAM,QAAM;AAGZ,YAAG,CAAC,MAAM,KAAI;AACb,2BAAiB,OAAO,QAAQ;AAChC,4BAAkB,OAAO,QAAQ;AACjC,cAAG,CAAC,QAAQ,iBAAiB,OAAO,QAAQ,GAAG,CAAC,IAAK,IAAK,KAAK,GAAI,CAAC,GAAE;AACrE;AAAA,UACD;AACA,2BAAiB,OAAO,QAAQ;AAChC,iBAAM,SAAS,CAAC,IAAE,MAAM,QAAO;AAC9B,gBAAI,OAAK,iBAAiB,OAAO,QAAQ;AACzC,gBAAI,SAAO,kBAAkB,OAAO,QAAQ;AAC5C,gBAAI,OAAK,CAAC,CAAC,GAAE,WAAS;AACtB,gBAAG,CAAC,OAAO;AAEX,gBAAG,QAAQ,MAAM,CAAC,IAAK,IAAK,KAAK,GAAI,CAAC,GAAE;AACvC,qBAAM,KAAK,CAAC,IAAE,OAAO,QAAO;AAC3B,oBAAI,OAAK,iBAAiB,QAAQ,IAAI;AACtC,oBAAI,SAAO,kBAAkB,QAAQ,IAAI;AACzC,oBAAI,OAAK,CAAC,CAAC,GAAE,QAAM,EAAC,UAAS,GAAE,YAAW,EAAC;AAC3C,oBAAG,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AACxB,yBAAM,KAAK,CAAC,IAAE,OAAO,QAAO;AAC3B,wBAAI,OAAK,iBAAiB,QAAQ,IAAI;AACtC,wBAAI,SAAO,kBAAkB,QAAQ,IAAI;AACzC,wBAAI,OAAK,CAAC,CAAC;AACX,wBAAG,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AACxB,0BAAI,MAAI,SAAS,MAAM;AACvB,4BAAM,SAAO;AACb,6BAAO,GAAG,IAAE;AAAA,oBACb,WAAS,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AAC9B,0BAAI,MAAI,SAAS,MAAM;AACvB,0BAAG,OAAK,EAAG,OAAM,OAAK;AAAA,+BACd,OAAK,GAAG;AACf,8BAAM,OAAK;AACX,4BAAG,CAAC,SAAU,OAAM,SAAO;AAC3B,8BAAM,MAAI;AAAA,sBACX,MAAM,OAAM,OAAK,UAAQ;AAAA,oBAC1B,WAAS,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AAC9B,0BAAI,MAAI;AACR,+BAAQ,IAAE,GAAE,IAAE,OAAO,QAAO,KAAI;AAC/B,+BAAK,OAAO,aAAa,OAAO,CAAC,CAAC;AAAA,sBACnC;AACA,4BAAM,QAAM;AAAA,oBACb,WAAS,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AAC9B,6BAAM,KAAK,CAAC,IAAE,OAAO,QAAO;AAC3B,4BAAI,OAAK,iBAAiB,QAAQ,IAAI;AACtC,4BAAI,SAAO,kBAAkB,QAAQ,IAAI;AAEzC,4BAAG,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AACxB,8BAAI,MAAI,GAAE,MAAI,IAAI,WAAW,OAAO,QAAQ,CAAC,EAAE;AAC/C,8BAAG,OAAO,UAAQ,EAAG,OAAI,IAAI,aAAa,GAAG,EAAE,CAAC;AAAA,mCACxC,OAAO,UAAQ,EAAG,OAAI,IAAI,aAAa,GAAG,EAAE,CAAC;AAAA,8BAChD,MAAK,qBAAoB,GAAE,MAAM;AACtC,gCAAM,aAAa,IAAE,KAAK,MAAM,GAAG;AAAA,wBACpC,WAAS,QAAQ,MAAM,CAAC,IAAK,GAAI,CAAC,EAAG,OAAM,WAAS,SAAS,MAAM;AAAA,iCAC3D,QAAQ,MAAM,CAAC,GAAI,CAAC,EAAG,OAAM,WAAS,SAAS,MAAM;AAAA,sBAC9D;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAAC;AACD,oBAAM,MAAI;AACV,mBAAK,eAAc,MAAM;AACzB,qBAAO;AACP;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA,YAAI,SAAO,MAAM;AACjB,YAAG,CAAC,OAAO;AACX,YAAI,UAAQ,OAAO,aAAa;AAAG,cAAM,SAAO;AAChD,YAAG,OAAO,YAAU,MAAM,SAAS,KAAK,OAAO,KAAK,GAAE;AACrD,iBAAO,WAAS;AAChB,eAAK,mBAAkB,CAAC;AAAA,QACzB;AACA,YAAG,UAAQ,OAAQ,OAAO,YAAU,MAAM,OAAO,WAAS,KAAK,CAAC,eAAe,KAAK,OAAO,KAAK,GAAE;AACjG,gBAAM,QAAM,CAAC;AACb,cAAG,CAAC,MAAM,IAAI,MAAK,yBAAwB,GAAE,KAAK;AAClD,gBAAM,MAAI;AACV,iBAAO;AAAA,QACR;AAGA,YAAI,QAAM,CAAC,GAAE,UAAQ;AACrB,eAAM,SAAS,CAAC,IAAE,MAAM,QAAO;AAC9B,cAAI,OAAK,iBAAiB,OAAO,QAAQ;AACzC,cAAI,SAAO,kBAAkB,OAAO,QAAQ;AAC5C,cAAG,CAAC,OAAO;AACX,cAAG,QAAQ,MAAM,CAAC,GAAI,CAAC,GAAE;AACxB,gBAAI,UAAQ,OAAO,CAAC,IAAE;AACtB,gBAAI,QAAM,OAAO,OAAO;AACxB,gBAAG,CAAC,OAAM;AACT,mBAAK,gBAAc,SAAQ,GAAE,MAAM;AACnC,qBAAO;AAAA,YACR,WAAS,MAAM,QAAM,GAAE;AACtB,kBAAI,QAAM,IAAI,WAAW,OAAO,SAAO,CAAC;AACxC,uBAAQ,IAAE,GAAE,IAAE,OAAO,QAAO,KAAI;AAC/B,sBAAM,IAAE,CAAC,IAAE,OAAO,CAAC;AAAA,cACpB;AACA,oBAAM,KAAK,KAAK;AAAG,yBAAS,MAAM;AAAA,YACnC;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAEA,YAAG,SAAQ;AACV,cAAI,OAAK,IAAI,WAAW,MAAM,SAAO,MAAM,IAAI,CAAC,CAAC;AACjD,eAAK,IAAI,MAAM,SAAS,MAAM,IAAI,CAAC,CAAC,CAAC;AACrC,gBAAM,QAAM;AACZ,gBAAM,IAAI,CAAC,IAAE;AAEb,cAAI,QAAM,IAAI,WAAW,OAAO;AAChC,mBAAQ,IAAE,GAAE,KAAG,GAAE,IAAE,MAAM,QAAO,KAAI;AACnC,kBAAM,IAAI,MAAM,CAAC,GAAE,EAAE;AACrB,kBAAI,MAAM,CAAC,EAAE;AAAA,UACd;AACA,cAAI,MAAI,IAAI,aAAa,MAAM,MAAM;AAErC,cAAG,OAAO,WAAS,GAAE;AACpB,gBAAI,OAAK,CAAC;AACV,qBAAQ,IAAE,GAAE,IAAE,IAAI,UAAQ;AACzB,mBAAK,KAAK,IAAI,CAAC,CAAC;AAChB,mBAAG,OAAO;AAAA,YACX;AACA,kBAAI,IAAI,aAAa,IAAI;AAAA,UAC1B;AAAC;AACD,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,UAAI,UAAQ,SAAS,QAAO,QAAO;AAClC,YAAG,CAAC,UAAU,OAAO,UAAQ,OAAO,OAAQ,QAAO;AACnD,YAAG,OAAO,UAAQ,EAAG,QAAO,OAAO,CAAC,KAAG,OAAO,CAAC;AAC/C,iBAAQ,IAAE,GAAE,IAAE,OAAO,QAAO,KAAI;AAC/B,cAAG,OAAO,CAAC,KAAG,OAAO,CAAC,EAAG,QAAO;AAAA,QACjC;AACA,eAAO;AAAA,MACR;AAEA,UAAI,WAAS,SAAS,OAAM;AAC3B,YAAI,IAAE;AACN,iBAAQ,IAAE,GAAE,IAAE,MAAM,QAAO,KAAI;AAAC,cAAI,IAAE,MAAM,CAAC;AAAE,gBAAI,IAAE,KAAG,MAAI,MAAI,EAAE,SAAS,EAAE;AAAA,QAAC;AAAC;AAC/E,eAAO,SAAS,GAAE,EAAE,KAAG;AAAA,MACxB;AAEA,UAAI,mBAAiB,SAAS,KAAI,KAAI,MAAK;AAC1C,YAAI,IAAE,IAAI,CAAC;AACX,YAAG,KAAG,IAAI,OAAO;AACjB,YAAI,KAAG,IAAI,CAAC,GAAE,MAAI,YAAU,GAAG,SAAS,CAAC,GAAG,OAAO,EAAE;AACrD,YAAI,IAAE,eAAe,KAAK,EAAE;AAC5B,YAAG,CAAC,EAAE;AACN,YAAI,MAAI,EAAE,CAAC,EAAE,QAAQ,MAAI,CAAC;AAC1B,YAAG,IAAE,MAAI,IAAI,OAAO;AACpB,iBAAQ,KAAG,GAAE,KAAG,KAAI,MAAK;AAAE,cAAI,EAAE,IAAE,IAAI,CAAC;AAAG;AAAA,QAAK;AAChD,YAAG,KAAM,KAAI,CAAC,IAAE,SAAS,EAAE,CAAC,KAAG,KAAI,CAAC;AACpC,YAAI,CAAC,IAAE;AACP,eAAO;AAAA,MACR;AAEA,UAAI,oBAAkB,SAAS,KAAI,KAAI;AACtC,YAAI,SAAO,iBAAiB,KAAI,KAAI,CAAC;AACrC,YAAG,CAAC,OAAO;AACX,YAAI,MAAI,SAAS,MAAM;AACvB,YAAI,IAAE,IAAI,CAAC,GAAG,MAAI,CAAC;AACnB,YAAG,MAAI,YAAW;AACjB,cAAG,IAAE,MAAI,IAAI,OAAO;AACpB,mBAAQ,KAAG,GAAE,KAAG,KAAI,MAAK;AAAE,gBAAI,EAAE,IAAE,IAAI,CAAC;AAAG;AAAA,UAAK;AAAA,QACjD;AACA,YAAI,CAAC,IAAE;AACP,eAAO;AAAA,MACR;AAKA,UAAI,OAAK,SAAS,OAAK;AAAA,QACtB,MAAM;AAAA,QACL,OAAM,EAAC,SAAQ,MAAK,SAAQ,KAAI;AAAA,QAChC,SAAQ,CAAC;AAAA,QACT,MAAK,CAAC;AAAA,QAEN,KAAI,SAAS,KAAI,OAAM;AACvB,cAAI,MAAI,SAAO;AACf,cAAI,YAAU,IAAI;AAAW,sBAAU,aAAW,QAAM;AACxD,cAAI,OAAK,IAAI;AAAM,iBAAK,KAAK,MAAM,IAAI,KAAG;AAC1C,cAAG,CAAC,KAAK,OAAM,IAAI,MAAM,MAAI,WAAW;AACxC,cAAI,SAAO,KAAK,QAAQ,IAAI;AAC5B,cAAG,CAAC,QAAO;AAAE,qBAAO,CAAC;AAAG,iBAAK,QAAQ,IAAI,IAAE;AAAA,UAAO;AAAC;AACnD,cAAI,MAAI,eAAc;AACtB,mBAAQ,IAAE,GAAE,IAAE,MAAM,QAAO,KAAI;AAC9B,gBAAI,IAAE,MAAM,CAAC;AAAG,gBAAE,IAAI,KAAK,CAAC;AAC5B,gBAAG,CAAC,GAAE;AAAE,mBAAK,MAAI,aAAW,GAAE,GAAE,GAAG;AAAG;AAAA,YAAS;AAC/C,gBAAI,MAAI,EAAE,CAAC,GAAE,IAAE,EAAE,OAAO,IAAI,SAAO,CAAC;AACpC,gBAAG,CAAC,aAAa,OAAO,GAAG,EAAG;AAC9B,mBAAO,GAAG,IAAE;AAAA,UACb;AAAA,QACD;AAAA,QAEC,KAAI,WAA2B;AAC/B,iBAAO,KAAK,IAAI,MAAM,MAAK,SAAS;AAAA,QACrC;AAAA,QAAG,KAAI,SAAS,KAAI,MAAK,MAAK;AAC7B,iBAAK,QAAM,CAAC;AACZ,iBAAK,QAAM,KAAK;AAAM,iBAAK,KAAK,MAAM,IAAI,KAAG;AAC7C,cAAI,SAAO,KAAK,QAAQ,IAAI;AAC5B,cAAI,MAAI,UAAQ,OAAO,GAAG,KAAG;AAC7B,cAAG,CAAC,OAAK,QAAM,MAAK;AACnB,gBAAG,QAAM,KAAK,QAAO,KAAK,IAAI,KAAI,MAAK,IAAI;AAC3C,mBAAO,KAAK,IAAI,KAAI,MAAK,IAAI;AAAA,UAC9B;AACA,eAAK,WAAS;AACd,cAAG,OAAK,SAAS,QAAO;AACxB,iBAAO,IAAI,QAAQ,mBAAkB,SAAS,GAAE,GAAE,GAAE;AACnD,gBAAE,CAAC,KAAG;AAAG,gBAAE,KAAK,IAAE,CAAC;AACnB,gBAAG,IAAE,KAAK,IAAE,KAAK,QAAO;AAAE,kBAAE;AAAO,mBAAK,UAAQ,MAAI,WAAS,IAAE,QAAM,KAAI,CAAC;AAAA,YAAE;AAC5E,mBAAO,IAAE,KAAG;AAAA,UACb,CAAC;AAAA,QACF;AAAA,QASC,IAAG,WAAU;AACb,iBAAO,KAAK,IAAI,MAAM,MAAK,SAAS;AAAA,QACrC;AAAA,QAAG,KAAI,WAAU;AAChB,cAAI,IAAE,WAAU,MAAI,IAAG,OAAK,CAAC,GAAE,SAAO,GAAE,MAAI,SAAO;AACnD,cAAI,MAAI,eAAc;AACtB,mBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAC1B,gBAAI,IAAE,EAAE,CAAC;AACT,gBAAG,KAAG,GAAE;AACP,kBAAE,IAAI,KAAK,CAAC;AAAG,oBAAI,KAAG,EAAE,CAAC;AACzB,kBAAG,CAAC,IAAI,OAAM,IAAI,MAAM,MAAI,WAAW;AACvC,kBAAE,EAAE,OAAO,IAAI,SAAO,CAAC;AAAA,YACxB;AACA,gBAAG,WAAS,GAAI,MAAK,KAAK,CAAC;AAAA,qBACnB,OAAQ,OAAM,IAAI,MAAM,MAAI,WAAW;AAAA,qBACvC,MAAI,EAAG,UAAO;AAAA,qBACd,MAAM,CAAC,GAAE;AAChB,kBAAG,IAAE,EAAG,OAAM,IAAI,MAAM,MAAI,WAAW;AACvC,uBAAO;AAAA,YACR,OAAK;AACJ,kBAAI,OAAK,KAAG,IAAE,OAAK,IAAE,KAAG;AACxB,kBAAE,IAAI,KAAK,CAAC;AAAG,kBAAG,GAAE;AAAE,uBAAK,EAAE,CAAC,KAAG;AAAM,oBAAE,EAAE,OAAO,EAAE,CAAC,EAAE,SAAO,CAAC;AAAA,cAAG;AAClE,kBAAG,CAAC,KAAK,CAAC,KAAK,OAAM,IAAI,MAAM,MAAI,IAAE,WAAW;AAChD,mBAAK,IAAI,EAAC,MAAU,WAAU,MAAK,GAAE,CAAC,MAAI,MAAI,CAAC,CAAC;AAAA,YACjD;AAAA,UACD;AACA,cAAG,CAAC,IAAI,QAAO;AACf,cAAG,SAAO,EAAE,QAAO;AACnB,iBAAO,KAAK,IAAI,KAAI,IAAI;AAAA,QACzB;AAAA,MACD;AACA,UAAI,KAAG,KAAK;AAAI,SAAG,IAAE,KAAK;AAE1B,SAAG,+BAA8B,CAAC;AAClC,SAAG,2BAA0B,CAAC;AAC9B,SAAG,+BAA8B,CAAC;AAClC,SAAG,yBAAwB,CAAC;AAM5B,eAAS,gBAAc;AACvB,UAAI,UAAQ,SAAS,UAAQ,SAAS,QAAO;AAC5C,YAAG,CAAC,UAAU;AACd,iBAAO,SAAO,MAAI,SAAO,aAAW,SAAO;AAC3C,YAAI,SAAO,SAAS;AACpB,YAAG,QAAO;AACT,cAAI,OAAK,SAAS;AAClB,cAAI,IAAE,+BAA+B,KAAK,SAAS,IAAI,KAAG,CAAC;AAC3D,cAAI,OAAM,EAAE,CAAC,KAAG;AAChB,cAAI,OAAK,EAAE,CAAC,KAAG,QAAM;AAErB,cAAG,OAAO,QAAQ,IAAI,KAAG,GAAE;AAE1B,gBAAG,WAAW,KAAK,GAAG,GAAE;AACvB,uBAAO,WAAS;AAAA,YACjB,OAAK;AACJ,uBAAO,UAAQ;AAAA,YAChB;AAAC;AAAA,UACF;AAAC;AACD,cAAG,QAAO;AACT,qBAAO,SAAO,SAAO,mBAAmB,OAAK,MAAM;AAAA,UACpD;AAAC;AAED,cAAG,CAAC,KAAK,GAAG,GAAE;AACb,iBAAK,GAAG,IAAE;AAEV,gBAAI,MAAI,IAAI,MAAM;AAClB,gBAAI,MAAI;AACR,iBAAK,8BAA4B,UAAQ,SAAO,oBAAkB,SAAS,cAAc;AAAA,UAC1F;AAAC;AAAA,QACF;AAAC;AAAA,MACF;AAIA,UAAG,OAAM;AACR,aAAK,GAAG,iBAAgB,GAAE,MAAM,GAAE,CAAC;AACnC,cAAM,QAAQ;AAAA,MACf;AAAC;AACD,aAAO,MAAM,IAAE;AAAA,IAEf,CAAC;AAAA;AAAA;", "names": ["ctx", "bufferSize", "stream", "hasClear", "i", "duration"]}