<script setup lang="ts">
import AudioRecorder from '@/components/AudioRecorder.vue'

/**
 * 处理录音完成事件
 * @param blob 录音文件的 Blob 对象
 * @param duration 录音时长（秒）
 */
const handleRecordingComplete = (blob: Blob, duration: number): void => {
  console.log('录音完成回调:', {
    size: blob.size,
    type: blob.type,
    duration: duration,
  })

  // 这里可以处理录音完成后的逻辑
  // 例如：上传到服务器、保存到本地、播放录音等

  // 示例：创建下载链接（可选）
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `recording_${new Date().getTime()}.wav`
  // document.body.appendChild(a)
  // a.click()
  // document.body.removeChild(a)
  // URL.revokeObjectURL(url)

  alert(`录音完成！时长: ${duration}秒，文件大小: ${(blob.size / 1024).toFixed(2)}KB`)
}

/**
 * 处理录音开始事件
 */
const handleRecordingStart = (): void => {
  console.log('录音开始')
}

/**
 * 处理录音错误事件
 * @param error 错误信息
 */
const handleRecordingError = (error: string): void => {
  console.error('录音错误:', error)
}
</script>

<template>
  <div class="app">
    <AudioRecorder
      @recording-complete="handleRecordingComplete"
      @recording-start="handleRecordingStart"
      @recording-error="handleRecordingError"
    />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
}

#app {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}
</style>
