import Recorder from 'recorder-core'
import 'recorder-core/src/engine/wav'
import 'recorder-core/src/extensions/waveview'
import { ref } from 'vue'
import { RecordingState } from '@/constants/app.constants'

// 定义 Recorder 的配置类型
type RecorderConfig = {
  type: string
  sampleRate: number
  bitRate: number
  onProcess: (
    buffer: number[],
    powerLevel: number,
    bufferDuration: number,
    bufferSampleRate: number,
  ) => void
  sourceStream?: MediaStream
  audioTrackSet?: {
    channelCount: number
    echoCancellation: boolean
    noiseSuppression: boolean
    autoGainControl: boolean
  }
}

// 定义 Recorder 接口类型
interface RecorderInstance {
  open: (success?: () => void, error?: (error: Error) => void) => void
  close: () => void
  start: () => void
  stop: (callback: (blob: Blob, duration: number) => void) => void
  pause: () => void
  resume: () => void
}

// 不需要单独的类型声明，直接在使用时进行类型断言

export function useRecorder(options?: {
  onAudioProcess?: (chunk: Int16Array) => void
  onAudioEnd?: () => void
}) {
  let rec: RecorderInstance | null = null
  let sampleBuf = new Int16Array()

  const audioState = ref<RecordingState>(RecordingState.STOPPED)
  const duration = ref(0)
  // 获取录音权限以及音频配置
  async function getMediaStream() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1, // 单声道
          echoCancellation: true, // 回声消除
          noiseSuppression: true, // 噪声抑制
          autoGainControl: true, // 自动调节音量
          // sampleRate: 44100 // 设置固定采样率
        },
      })
      return stream
    } catch (error) {
      console.error('获取音频流失败:', error)
      return null
    }
  }

  const open = async (success: () => void) => {
    // 如果已存在实例，先关闭
    if (rec) {
      rec.close()
      rec = null
    }

    try {
      // 先获取媒体流
      const stream = await getMediaStream()
      if (!stream) {
        audioState.value = RecordingState.ERROR
        alert("无法获取音频流")

        throw new Error('无法获取音频流')
      }


      rec = new (Recorder as { new(config: RecorderConfig): RecorderInstance })({
        type: 'wav',
        sampleRate: 44100,
        bitRate: 256,
        onProcess: recProcess,
        sourceStream: stream, // 直接使用获取到的流
        audioTrackSet: {
          channelCount: 1,
          echoCancellation: true, // 回声消除
          noiseSuppression: true, // 噪声抑制
          autoGainControl: true, // 自动调节音量
        },
      })

      rec.open(
        () => {
          console.log('录音初始化成功')
          if (success) success()
        },
        (error: Error) => {
          console.error('录音初始化失败:', error)
          // 如果失败，尝试使用默认配置，针对蓝牙耳机适配
          const defaultConfig = {
            type: 'wav',
            sampleRate: 16000,
            bitRate: 16,
            onProcess: recProcess,
          }
          rec = new (Recorder as { new(config: RecorderConfig): RecorderInstance })(defaultConfig)
          if (rec) {
            rec.open(success, (error: Error) => {
              console.error('录音完全初始化失败:', error)
            })
          }
        },
      )
    } catch (error) {

      console.error('录音设备初始化失败:', error)
      throw error
    }
  }

  // 开始录音
  const recStart = () => {
    if (rec) {
      rec.start()
    }
  }

  // 停止录音
  const recStop = (onComplete?: (blob: Blob, duration: number) => void) => {
    if (sampleBuf.length > 0) {
      sampleBuf = new Int16Array()
      options?.onAudioProcess?.(sampleBuf)
    }
    options?.onAudioEnd?.()
    if (rec) {
      rec.stop((blob, duration) => {
        console.log('录音结束，时长:', duration, '秒')
        // 调用回调函数返回 blob
        if (onComplete) {
          onComplete(blob, duration)
        }
        /*** 【立即播放例子】 ***/
        // const localUrl = (window.URL || webkitURL).createObjectURL(blob);
        // const audio = document.createElement("audio");
        // document.body.prepend(audio);
        // audio.controls = true;
        // audio.src = localUrl;
        // audio.play();
      })
    }
    audioState.value = RecordingState.STOPPED
  }

  // 开始录音
  const recOpen = async () => {
    try {
      await open(() => {
        console.log(1);

        recStart()
        audioState.value = RecordingState.RECORDING

      })
    } catch (error) {
      console.error('打开录音失败:', error)
      audioState.value = RecordingState.STOPPED
    }
  }

  // 录音处理
  function recProcess(
    buffer: number[],
    powerLevel: number,
    bufferDuration: number,
    bufferSampleRate: number,
  ) {
    if (audioState.value === RecordingState.RECORDING) {
      const data_48k = buffer[buffer.length - 1]
      const array_48k = new Array(data_48k)
      // 使用类型断言确保 Recorder.SampleData 是可用的
      const data_16k = (
        Recorder as {
          SampleData: (
            data: number[][],
            fromSampleRate: number,
            toSampleRate: number,
          ) => { data: number[] }
        }
      ).SampleData(array_48k, bufferSampleRate, 16000).data

      sampleBuf = Int16Array.from([...sampleBuf, ...data_16k])
      const chunk_size = 960 // for asr chunk_size [5, 10, 5]
      while (sampleBuf.length >= chunk_size) {
        const sendBuf = sampleBuf.slice(0, chunk_size)
        sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length)
        options?.onAudioProcess?.(sendBuf)
      }
    }
  }
  // 暂停录音
  function recPause() {
    audioState.value = RecordingState.PAUSED
    if (rec) {
      rec.pause()
    }
  }
  // 恢复录音
  function recResume() {
    console.log('恢复录音')
    audioState.value = RecordingState.RECORDING
    rec?.resume()
  }

  return {
    audioState,
    recOpen,
    recStop,
    recPause,
    recResume,
  }
}
