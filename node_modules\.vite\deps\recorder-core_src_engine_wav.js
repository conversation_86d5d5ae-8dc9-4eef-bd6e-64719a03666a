// node_modules/recorder-core/src/engine/wav.js
(function(factory) {
  var browser = typeof window == "object" && !!window.document;
  var win = browser ? window : Object;
  var rec = win.Recorder, ni = rec.i18n;
  factory(rec, ni, ni.$T, browser);
})(function(Recorder, i18n, $T, isBrowser) {
  "use strict";
  Recorder.prototype.enc_wav = {
    stable: true,
    fast: true,
    getTestMsg: function() {
      return $T("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）");
    }
  };
  var NormalizeSet = function(set) {
    var bS = set.bitRate, b = bS == 8 ? 8 : 16;
    if (bS != b) Recorder.CLog($T("wyw9::WAV Info: 不支持{1}位，已更新成{2}位", 0, bS, b), 3);
    set.bitRate = b;
  };
  Recorder.prototype.wav = function(res, True, False) {
    var This = this, set = This.set;
    NormalizeSet(set);
    var size = res.length, sampleRate = set.sampleRate, bitRate = set.bitRate;
    var dataLength = size * (bitRate / 8);
    var header = Recorder.wav_header(1, 1, sampleRate, bitRate, dataLength);
    var offset = header.length;
    var bytes = new Uint8Array(offset + dataLength);
    bytes.set(header);
    if (bitRate == 8) {
      for (var i = 0; i < size; i++) {
        var val = (res[i] >> 8) + 128;
        bytes[offset++] = val;
      }
      ;
    } else {
      bytes = new Int16Array(bytes.buffer);
      bytes.set(res, offset / 2);
    }
    ;
    True(bytes.buffer, "audio/wav");
  };
  Recorder.wav_header = function(format, numCh, sampleRate, bitRate, dataLength) {
    var extSize = format == 1 ? 0 : 2;
    var buffer = new ArrayBuffer(44 + extSize);
    var data = new DataView(buffer);
    var offset = 0;
    var writeString = function(str) {
      for (var i = 0; i < str.length; i++, offset++) {
        data.setUint8(offset, str.charCodeAt(i));
      }
      ;
    };
    var write16 = function(v) {
      data.setUint16(offset, v, true);
      offset += 2;
    };
    var write32 = function(v) {
      data.setUint32(offset, v, true);
      offset += 4;
    };
    writeString("RIFF");
    write32(36 + extSize + dataLength);
    writeString("WAVE");
    writeString("fmt ");
    write32(16 + extSize);
    write16(format);
    write16(numCh);
    write32(sampleRate);
    write32(sampleRate * (numCh * bitRate / 8));
    write16(numCh * bitRate / 8);
    write16(bitRate);
    if (format != 1) {
      write16(0);
    }
    writeString("data");
    write32(dataLength);
    return new Uint8Array(buffer);
  };
});
//# sourceMappingURL=recorder-core_src_engine_wav.js.map
