{"version": 3, "sources": ["../../recorder-core/src/extensions/waveview.js"], "sourcesContent": ["/*\r\n录音 Recorder扩展，动态波形显示\r\nhttps://github.com/xiangyuecn/Recorder\r\n*/\r\n(function(factory){\r\n\tvar browser=typeof window==\"object\" && !!window.document;\r\n\tvar win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面\r\n\tvar rec=win.Recorder,ni=rec.i18n;\r\n\tfactory(rec,ni,ni.$T,browser);\r\n}(function(Recorder,i18n,$T,isBrowser){\r\n\"use strict\";\r\n\r\nvar WaveView=function(set){\r\n\treturn new fn(set);\r\n};\r\nvar ViewTxt=\"WaveView\";\r\nvar fn=function(set){\r\n\tvar This=this;\r\n\tvar o={\r\n\t\t/*\r\n\t\telem:\"css selector\" //自动显示到dom，并以此dom大小为显示大小\r\n\t\t\t//或者配置显示大小，手动把waveviewObj.elem显示到别的地方\r\n\t\t,width:0 //显示宽度\r\n\t\t,height:0 //显示高度\r\n\t\t\r\nH5环境以上配置二选一\r\n\t\t\r\n\t\tcompatibleCanvas: CanvasObject //提供一个兼容H5的canvas对象，需支持getContext(\"2d\")，支持设置width、height，支持drawImage(canvas,...)\r\n\t\t,width:0 //canvas显示宽度\r\n\t\t,height:0 //canvas显示高度\r\n非H5环境使用以上配置\r\n\t\t*/\r\n\t\t\r\n\t\tscale:2 //缩放系数，应为正整数，使用2(3? no!)倍宽高进行绘制，避免移动端绘制模糊\r\n\t\t,speed:9 //移动速度系数，越大越快\r\n\t\t,phase:21.8 //相位，调整了速度后，调整这个值得到一个看起来舒服的波形\r\n\t\t\r\n\t\t,fps:20 //绘制帧率，调整后也需调整phase值\r\n\t\t,keep:true //当停止了input输入时，是否保持波形，设为false停止后将变成一条线\r\n\t\t\r\n\t\t,lineWidth:3 //线条基础粗细\r\n\t\t\r\n\t\t//渐变色配置：[位置，css颜色，...] 位置: 取值0.0-1.0之间\r\n\t\t,linear1:[0,\"rgba(150,96,238,1)\",0.2,\"rgba(170,79,249,1)\",1,\"rgba(53,199,253,1)\"] //线条渐变色1，从左到右\r\n\t\t,linear2:[0,\"rgba(209,130,255,0.6)\",1,\"rgba(53,199,255,0.6)\"] //线条渐变色2，从左到右\r\n\t\t,linearBg:[0,\"rgba(255,255,255,0.2)\",1,\"rgba(54,197,252,0.2)\"] //背景渐变色，从上到下\r\n\t};\r\n\tfor(var k in set){\r\n\t\to[k]=set[k];\r\n\t};\r\n\tThis.set=set=o;\r\n\t\r\n\tvar cCanvas=\"compatibleCanvas\";\r\n\tif(set[cCanvas]){\r\n\t\tvar canvas=This.canvas=set[cCanvas];\r\n\t}else{\r\n\t\tif(!isBrowser)throw new Error($T.G(\"NonBrowser-1\",[ViewTxt]));\r\n\t\tvar elem=set.elem;\r\n\t\tif(elem){\r\n\t\t\tif(typeof(elem)==\"string\"){\r\n\t\t\t\telem=document.querySelector(elem);\r\n\t\t\t}else if(elem.length){\r\n\t\t\t\telem=elem[0];\r\n\t\t\t};\r\n\t\t};\r\n\t\tif(elem){\r\n\t\t\tset.width=elem.offsetWidth;\r\n\t\t\tset.height=elem.offsetHeight;\r\n\t\t};\r\n\t\t\r\n\t\tvar thisElem=This.elem=document.createElement(\"div\");\r\n\t\tthisElem.style.fontSize=0;\r\n\t\tthisElem.innerHTML='<canvas style=\"width:100%;height:100%;\"/>';\r\n\t\t\r\n\t\tvar canvas=This.canvas=thisElem.querySelector(\"canvas\");\r\n\t\t\r\n\t\tif(elem){\r\n\t\t\telem.innerHTML=\"\";\r\n\t\t\telem.appendChild(thisElem);\r\n\t\t};\r\n\t};\r\n\tvar scale=set.scale;\r\n\tvar width=set.width*scale;\r\n\tvar height=set.height*scale;\r\n\tif(!width || !height){\r\n\t\tthrow new Error($T.G(\"IllegalArgs-1\",[ViewTxt+\" width=0 height=0\"]));\r\n\t};\r\n\t\r\n\tcanvas.width=width;\r\n\tcanvas.height=height;\r\n\tvar ctx=This.ctx=canvas.getContext(\"2d\");\r\n\t\r\n\tThis.linear1=This.genLinear(ctx,width,set.linear1);\r\n\tThis.linear2=This.genLinear(ctx,width,set.linear2);\r\n\tThis.linearBg=This.genLinear(ctx,height,set.linearBg,true);\r\n\t\r\n\tThis._phase=0;\r\n};\r\nfn.prototype=WaveView.prototype={\r\n\tgenLinear:function(ctx,size,colors,top){\r\n\t\tvar rtv=ctx.createLinearGradient(0,0,top?0:size,top?size:0);\r\n\t\tfor(var i=0;i<colors.length;){\r\n\t\t\trtv.addColorStop(colors[i++],colors[i++]);\r\n\t\t};\r\n\t\treturn rtv;\r\n\t}\r\n\t,genPath:function(frequency,amplitude,phase){\r\n\t\t//曲线生成算法参考 https://github.com/HaloMartin/MCVoiceWave/blob/f6dc28975fbe0f7fc6cc4dbc2e61b0aa5574e9bc/MCVoiceWave/MCVoiceWaveView.m#L268\r\n\t\tvar rtv=[];\r\n\t\tvar This=this,set=This.set;\r\n\t\tvar scale=set.scale;\r\n\t\tvar width=set.width*scale;\r\n\t\tvar maxAmplitude=set.height*scale/2;\r\n\t\t\r\n\t\tfor(var x=0;x<=width;x+=scale) {\r\n\t\t\tvar scaling=(1+Math.cos(Math.PI+(x/width)*2*Math.PI))/2;\r\n\t\t\tvar y=scaling*maxAmplitude*amplitude*Math.sin(2*Math.PI*(x/width)*frequency+phase)+maxAmplitude;\r\n\t\t\trtv.push(y);\r\n\t\t}\r\n\t\treturn rtv;\r\n\t}\r\n\t,input:function(pcmData,powerLevel,sampleRate){\r\n\t\tvar This=this;\r\n\t\tThis.sampleRate=sampleRate;\r\n\t\tThis.pcmData=pcmData;\r\n\t\tThis.pcmPos=0;\r\n\t\t\r\n\t\tThis.inputTime=Date.now();\r\n\t\tThis.schedule();\r\n\t}\r\n\t,schedule:function(){\r\n\t\tvar This=this,set=This.set;\r\n\t\tvar interval=Math.floor(1000/set.fps);\r\n\t\tif(!This.timer){\r\n\t\t\tThis.timer=setInterval(function(){\r\n\t\t\t\tThis.schedule();\r\n\t\t\t},interval);\r\n\t\t};\r\n\t\t\r\n\t\tvar now=Date.now();\r\n\t\tvar drawTime=This.drawTime||0;\r\n\t\tif(now-drawTime<interval){\r\n\t\t\t//没到间隔时间，不绘制\r\n\t\t\treturn;\r\n\t\t};\r\n\t\tThis.drawTime=now;\r\n\t\t\r\n\t\t//切分当前需要的绘制数据\r\n\t\tvar bufferSize=This.sampleRate/set.fps;\r\n\t\tvar pcm=This.pcmData;\r\n\t\tvar pos=This.pcmPos;\r\n\t\tvar len=Math.max(0, Math.min(bufferSize,pcm.length-pos));\r\n\t\tvar sum=0;\r\n\t\tfor(var i=0;i<len;i++,pos++){\r\n\t\t\tsum+=Math.abs(pcm[pos]);\r\n\t\t};\r\n\t\tThis.pcmPos=pos;\r\n\t\t\r\n\t\t//推入绘制\r\n\t\tif(len || !set.keep){\r\n\t\t\tThis.draw(Recorder.PowerLevel(sum, len));\r\n\t\t}\r\n\t\tif(!len && now-This.inputTime>1300){\r\n\t\t\t//超时没有输入，干掉定时器\r\n\t\t\tclearInterval(This.timer);\r\n\t\t\tThis.timer=0;\r\n\t\t}\r\n\t}\r\n\t,draw:function(powerLevel){\r\n\t\tvar This=this,set=This.set;\r\n\t\tvar ctx=This.ctx;\r\n\t\tvar scale=set.scale;\r\n\t\tvar width=set.width*scale;\r\n\t\tvar height=set.height*scale;\r\n\t\t\r\n\t\tvar speedx=set.speed/set.fps;\r\n\t\tvar phase=This._phase-=speedx;//位移速度\r\n\t\tvar phase2=phase+speedx*set.phase;\r\n\t\tvar amplitude=powerLevel/100;\r\n\t\tvar path1=This.genPath(2,amplitude,phase);\r\n\t\tvar path2=This.genPath(1.8,amplitude,phase2);\r\n\t\t\r\n\t\t//开始绘制图形\r\n\t\tctx.clearRect(0,0,width,height);\r\n\t\t\r\n\t\t//绘制包围背景\r\n\t\tctx.beginPath();\r\n\t\tfor(var i=0,x=0;x<=width;i++,x+=scale) {\r\n\t\t\tif (x==0) {\r\n\t\t\t\tctx.moveTo(x,path1[i]);\r\n\t\t\t}else {\r\n\t\t\t\tctx.lineTo(x,path1[i]);\r\n\t\t\t};\r\n\t\t};\r\n\t\ti--;\r\n\t\tfor(var x=width-1;x>=0;i--,x-=scale) {\r\n\t\t\tctx.lineTo(x,path2[i]);\r\n\t\t};\r\n\t\tctx.closePath();\r\n\t\tctx.fillStyle=This.linearBg;\r\n\t\tctx.fill();\r\n\t\t\r\n\t\t//绘制线\r\n\t\tThis.drawPath(path2,This.linear2);\r\n\t\tThis.drawPath(path1,This.linear1);\r\n\t}\r\n\t,drawPath:function(path,linear){\r\n\t\tvar This=this,set=This.set;\r\n\t\tvar ctx=This.ctx;\r\n\t\tvar scale=set.scale;\r\n\t\tvar width=set.width*scale;\r\n\t\t\r\n\t\tctx.beginPath();\r\n\t\tfor(var i=0,x=0;x<=width;i++,x+=scale) {\r\n\t\t\tif (x==0) {\r\n\t\t\t\tctx.moveTo(x,path[i]);\r\n\t\t\t}else {\r\n\t\t\t\tctx.lineTo(x,path[i]);\r\n\t\t\t};\r\n\t\t};\r\n\t\tctx.lineWidth=set.lineWidth*scale;\r\n\t\tctx.strokeStyle=linear;\r\n\t\tctx.stroke();\r\n\t}\r\n};\r\nRecorder[ViewTxt]=WaveView;\r\n\r\n\t\r\n}));"], "mappings": ";CAIC,SAAS,SAAQ;AACjB,MAAI,UAAQ,OAAO,UAAQ,YAAY,CAAC,CAAC,OAAO;AAChD,MAAI,MAAI,UAAQ,SAAO;AACvB,MAAI,MAAI,IAAI,UAAS,KAAG,IAAI;AAC5B,UAAQ,KAAI,IAAG,GAAG,IAAG,OAAO;AAC7B,GAAE,SAAS,UAAS,MAAK,IAAG,WAAU;AACtC;AAEA,MAAI,WAAS,SAAS,KAAI;AACzB,WAAO,IAAI,GAAG,GAAG;AAAA,EAClB;AACA,MAAI,UAAQ;AACZ,MAAI,KAAG,SAAS,KAAI;AACnB,QAAI,OAAK;AACT,QAAI,IAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAeL,OAAM;AAAA,MACL,OAAM;AAAA,MACN,OAAM;AAAA,MAEN,KAAI;AAAA,MACJ,MAAK;AAAA,MAEL,WAAU;AAAA,MAGV,SAAQ,CAAC,GAAE,sBAAqB,KAAI,sBAAqB,GAAE,oBAAoB;AAAA,MAC/E,SAAQ,CAAC,GAAE,yBAAwB,GAAE,sBAAsB;AAAA,MAC3D,UAAS,CAAC,GAAE,yBAAwB,GAAE,sBAAsB;AAAA;AAAA,IAC9D;AACA,aAAQ,KAAK,KAAI;AAChB,QAAE,CAAC,IAAE,IAAI,CAAC;AAAA,IACX;AAAC;AACD,SAAK,MAAI,MAAI;AAEb,QAAI,UAAQ;AACZ,QAAG,IAAI,OAAO,GAAE;AACf,UAAI,SAAO,KAAK,SAAO,IAAI,OAAO;AAAA,IACnC,OAAK;AACJ,UAAG,CAAC,UAAU,OAAM,IAAI,MAAM,GAAG,EAAE,gBAAe,CAAC,OAAO,CAAC,CAAC;AAC5D,UAAI,OAAK,IAAI;AACb,UAAG,MAAK;AACP,YAAG,OAAO,QAAO,UAAS;AACzB,iBAAK,SAAS,cAAc,IAAI;AAAA,QACjC,WAAS,KAAK,QAAO;AACpB,iBAAK,KAAK,CAAC;AAAA,QACZ;AAAC;AAAA,MACF;AAAC;AACD,UAAG,MAAK;AACP,YAAI,QAAM,KAAK;AACf,YAAI,SAAO,KAAK;AAAA,MACjB;AAAC;AAED,UAAI,WAAS,KAAK,OAAK,SAAS,cAAc,KAAK;AACnD,eAAS,MAAM,WAAS;AACxB,eAAS,YAAU;AAEnB,UAAI,SAAO,KAAK,SAAO,SAAS,cAAc,QAAQ;AAEtD,UAAG,MAAK;AACP,aAAK,YAAU;AACf,aAAK,YAAY,QAAQ;AAAA,MAC1B;AAAC;AAAA,IACF;AAAC;AACD,QAAI,QAAM,IAAI;AACd,QAAI,QAAM,IAAI,QAAM;AACpB,QAAI,SAAO,IAAI,SAAO;AACtB,QAAG,CAAC,SAAS,CAAC,QAAO;AACpB,YAAM,IAAI,MAAM,GAAG,EAAE,iBAAgB,CAAC,UAAQ,mBAAmB,CAAC,CAAC;AAAA,IACpE;AAAC;AAED,WAAO,QAAM;AACb,WAAO,SAAO;AACd,QAAI,MAAI,KAAK,MAAI,OAAO,WAAW,IAAI;AAEvC,SAAK,UAAQ,KAAK,UAAU,KAAI,OAAM,IAAI,OAAO;AACjD,SAAK,UAAQ,KAAK,UAAU,KAAI,OAAM,IAAI,OAAO;AACjD,SAAK,WAAS,KAAK,UAAU,KAAI,QAAO,IAAI,UAAS,IAAI;AAEzD,SAAK,SAAO;AAAA,EACb;AACA,KAAG,YAAU,SAAS,YAAU;AAAA,IAC/B,WAAU,SAAS,KAAI,MAAK,QAAO,KAAI;AACtC,UAAI,MAAI,IAAI,qBAAqB,GAAE,GAAE,MAAI,IAAE,MAAK,MAAI,OAAK,CAAC;AAC1D,eAAQ,IAAE,GAAE,IAAE,OAAO,UAAQ;AAC5B,YAAI,aAAa,OAAO,GAAG,GAAE,OAAO,GAAG,CAAC;AAAA,MACzC;AAAC;AACD,aAAO;AAAA,IACR;AAAA,IACC,SAAQ,SAAS,WAAU,WAAU,OAAM;AAE3C,UAAI,MAAI,CAAC;AACT,UAAI,OAAK,MAAK,MAAI,KAAK;AACvB,UAAI,QAAM,IAAI;AACd,UAAI,QAAM,IAAI,QAAM;AACpB,UAAI,eAAa,IAAI,SAAO,QAAM;AAElC,eAAQ,IAAE,GAAE,KAAG,OAAM,KAAG,OAAO;AAC9B,YAAI,WAAS,IAAE,KAAK,IAAI,KAAK,KAAI,IAAE,QAAO,IAAE,KAAK,EAAE,KAAG;AACtD,YAAI,IAAE,UAAQ,eAAa,YAAU,KAAK,IAAI,IAAE,KAAK,MAAI,IAAE,SAAO,YAAU,KAAK,IAAE;AACnF,YAAI,KAAK,CAAC;AAAA,MACX;AACA,aAAO;AAAA,IACR;AAAA,IACC,OAAM,SAAS,SAAQ,YAAW,YAAW;AAC7C,UAAI,OAAK;AACT,WAAK,aAAW;AAChB,WAAK,UAAQ;AACb,WAAK,SAAO;AAEZ,WAAK,YAAU,KAAK,IAAI;AACxB,WAAK,SAAS;AAAA,IACf;AAAA,IACC,UAAS,WAAU;AACnB,UAAI,OAAK,MAAK,MAAI,KAAK;AACvB,UAAI,WAAS,KAAK,MAAM,MAAK,IAAI,GAAG;AACpC,UAAG,CAAC,KAAK,OAAM;AACd,aAAK,QAAM,YAAY,WAAU;AAChC,eAAK,SAAS;AAAA,QACf,GAAE,QAAQ;AAAA,MACX;AAAC;AAED,UAAI,MAAI,KAAK,IAAI;AACjB,UAAI,WAAS,KAAK,YAAU;AAC5B,UAAG,MAAI,WAAS,UAAS;AAExB;AAAA,MACD;AAAC;AACD,WAAK,WAAS;AAGd,UAAI,aAAW,KAAK,aAAW,IAAI;AACnC,UAAI,MAAI,KAAK;AACb,UAAI,MAAI,KAAK;AACb,UAAI,MAAI,KAAK,IAAI,GAAG,KAAK,IAAI,YAAW,IAAI,SAAO,GAAG,CAAC;AACvD,UAAI,MAAI;AACR,eAAQ,IAAE,GAAE,IAAE,KAAI,KAAI,OAAM;AAC3B,eAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,MACvB;AAAC;AACD,WAAK,SAAO;AAGZ,UAAG,OAAO,CAAC,IAAI,MAAK;AACnB,aAAK,KAAK,SAAS,WAAW,KAAK,GAAG,CAAC;AAAA,MACxC;AACA,UAAG,CAAC,OAAO,MAAI,KAAK,YAAU,MAAK;AAElC,sBAAc,KAAK,KAAK;AACxB,aAAK,QAAM;AAAA,MACZ;AAAA,IACD;AAAA,IACC,MAAK,SAAS,YAAW;AACzB,UAAI,OAAK,MAAK,MAAI,KAAK;AACvB,UAAI,MAAI,KAAK;AACb,UAAI,QAAM,IAAI;AACd,UAAI,QAAM,IAAI,QAAM;AACpB,UAAI,SAAO,IAAI,SAAO;AAEtB,UAAI,SAAO,IAAI,QAAM,IAAI;AACzB,UAAI,QAAM,KAAK,UAAQ;AACvB,UAAI,SAAO,QAAM,SAAO,IAAI;AAC5B,UAAI,YAAU,aAAW;AACzB,UAAI,QAAM,KAAK,QAAQ,GAAE,WAAU,KAAK;AACxC,UAAI,QAAM,KAAK,QAAQ,KAAI,WAAU,MAAM;AAG3C,UAAI,UAAU,GAAE,GAAE,OAAM,MAAM;AAG9B,UAAI,UAAU;AACd,eAAQ,IAAE,GAAE,IAAE,GAAE,KAAG,OAAM,KAAI,KAAG,OAAO;AACtC,YAAI,KAAG,GAAG;AACT,cAAI,OAAO,GAAE,MAAM,CAAC,CAAC;AAAA,QACtB,OAAM;AACL,cAAI,OAAO,GAAE,MAAM,CAAC,CAAC;AAAA,QACtB;AAAC;AAAA,MACF;AAAC;AACD;AACA,eAAQ,IAAE,QAAM,GAAE,KAAG,GAAE,KAAI,KAAG,OAAO;AACpC,YAAI,OAAO,GAAE,MAAM,CAAC,CAAC;AAAA,MACtB;AAAC;AACD,UAAI,UAAU;AACd,UAAI,YAAU,KAAK;AACnB,UAAI,KAAK;AAGT,WAAK,SAAS,OAAM,KAAK,OAAO;AAChC,WAAK,SAAS,OAAM,KAAK,OAAO;AAAA,IACjC;AAAA,IACC,UAAS,SAAS,MAAK,QAAO;AAC9B,UAAI,OAAK,MAAK,MAAI,KAAK;AACvB,UAAI,MAAI,KAAK;AACb,UAAI,QAAM,IAAI;AACd,UAAI,QAAM,IAAI,QAAM;AAEpB,UAAI,UAAU;AACd,eAAQ,IAAE,GAAE,IAAE,GAAE,KAAG,OAAM,KAAI,KAAG,OAAO;AACtC,YAAI,KAAG,GAAG;AACT,cAAI,OAAO,GAAE,KAAK,CAAC,CAAC;AAAA,QACrB,OAAM;AACL,cAAI,OAAO,GAAE,KAAK,CAAC,CAAC;AAAA,QACrB;AAAC;AAAA,MACF;AAAC;AACD,UAAI,YAAU,IAAI,YAAU;AAC5B,UAAI,cAAY;AAChB,UAAI,OAAO;AAAA,IACZ;AAAA,EACD;AACA,WAAS,OAAO,IAAE;AAGlB,CAAC;", "names": []}